import os
import sys
import time
import torch
from itertools import product

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from config import Config, create_config_from_presets, get_gnn_config_presets, get_snn_config_presets
from model import MyCas
from data_processor import load_cascade_data, preprocess_data

def save_result(filename, content):
    """Save results to file"""
    os.makedirs(os.path.dirname(filename) if os.path.dirname(filename) else '.', exist_ok=True)
    with open(filename, 'a') as f:
        f.write(content)

def run_comparison_experiments(dataset):
    """Run comparison experiments with different GNN and SNN configurations"""
    print(f"\n=== Running Comparison Experiments for {dataset} ===")
    
    # Basic parameters
    if dataset == 'weibo':
        pred_time, obs_time, time_interval = 24*3600, 1*3600, 36
    else:  # twitter
        pred_time, obs_time, time_interval = 3600*24*32, 1*24*3600, 900
    
    # Get all available presets
    gnn_presets = list(get_gnn_config_presets().keys())
    snn_presets = list(get_snn_config_presets().keys())
    
    print(f"GNN presets to test: {gnn_presets}")
    print(f"SNN presets to test: {snn_presets}")
    
    results = []
    
    # Test different combinations
    for gnn_preset in gnn_presets:
        for snn_preset in snn_presets:
            print(f"\n--- Testing GNN: {gnn_preset}, SNN: {snn_preset} ---")
            
            try:
                # Create config with presets
                config = create_config_from_presets(
                    dataset, pred_time, obs_time, time_interval,
                    gnn_preset=gnn_preset, snn_preset=snn_preset,
                    epochs=50  # Reduced for comparison
                )
                
                print(f"Config: {config.toString()}")
                
                # Load data
                X, Y = load_cascade_data(config)
                X, Y = preprocess_data(X, Y, config)
                
                # Train model
                model = MyCas(config)
                train_msle, val_msle, test_msle, test_mape = model.train_model(X, Y)
                
                result = {
                    'gnn_preset': gnn_preset,
                    'snn_preset': snn_preset,
                    'test_msle': test_msle,
                    'test_mape': test_mape,
                    'config': config.toString()
                }
                results.append(result)
                
                # Save result
                result_str = f"{gnn_preset}+{snn_preset}: {test_msle:.4f}/{test_mape:.4f}\n"
                save_result(f"mycas/{dataset}_comparison.txt", result_str)
                
                print(f"Result: {test_msle:.4f}/{test_mape:.4f}")
                
            except Exception as e:
                print(f"Failed: {e}")
                continue
    
    # Find best combination
    if results:
        best_result = min(results, key=lambda x: x['test_msle'])
        print(f"\n=== Best Combination ===")
        print(f"GNN: {best_result['gnn_preset']}, SNN: {best_result['snn_preset']}")
        print(f"Performance: {best_result['test_msle']:.4f}/{best_result['test_mape']:.4f}")
        
        best_str = f"\nBEST: {best_result['gnn_preset']}+{best_result['snn_preset']}: "
        best_str += f"{best_result['test_msle']:.4f}/{best_result['test_mape']:.4f}\n"
        save_result(f"mycas/{dataset}_comparison.txt", best_str)

def main():
    # Parse command line arguments
    if len(sys.argv) > 1:
        dataset = sys.argv[1]
        if len(sys.argv) > 2:
            os.environ["CUDA_VISIBLE_DEVICES"] = sys.argv[2]
        
        # Check if comparison mode
        if len(sys.argv) > 3 and sys.argv[3] == 'compare':
            run_comparison_experiments(dataset)
            return
    else:
        dataset = 'twitter'  # default
        if torch.cuda.is_available():
            os.environ["CUDA_VISIBLE_DEVICES"] = '0'
    
    print(f"Running MyCas model on {dataset} dataset")
    print(f"CUDA available: {torch.cuda.is_available()}")
    
    # ============ EASY CONFIGURATION SECTION ============
    # Change these presets to test different combinations:
    
    GNN_PRESET = 'gat_gcn'      # Options: 'gat_gcn', 'gcn_only', 'gat_only', 
                                #          'sage_gin', 'transformer', 'residual_gat'
    
    SNN_PRESET = 'lif_sigmoid'  # Options: 'lif_sigmoid', 'lif_superspike', 
                                #          'adaptive_multigauss', 'if_triangle'
    
    # Or use manual configuration:
    MANUAL_CONFIG = {
        'gnn_first_layer_type': 'gat',        # 'gcn', 'gat', 'sage', 'gin', 'transformer'
        'gnn_layer_type': 'gcn',              # 'gcn', 'gat', 'sage', 'gin', 'transformer'  
        'graph_pooling_type': 'mean_max',     # 'mean', 'max', 'add', 'mean_max', 'mean_max_add'
        'snn_neuron_type': 'lif',             # 'lif', 'if', 'adaptive'
        'snn_surrogate_type': 'sigmoid',      # 'sigmoid', 'superspike', 'multigauss', 'triangle'
        'snn_temporal_integration': 'weighted' # 'weighted', 'attention', 'mean', 'sum'
    }
    
    # ====================================================
    
    # Hyperparameter configurations
    if dataset == 'weibo':
        hyperparameters = dict(
            dataset=['weibo'],
            K=[3],  # Number of runs
            predict_time=[24*3600],
            observation_time=[1*3600],
            time_interval=[36],
            gnn_hidden_size=[128],
            snn_hidden_size=[64],
            num_gnn_layers=[2],
            num_snn_layers=[2],
            num_attention_heads=[4],
            mlp=[[128, 64, 1]],
            lr=[0.0005],
            batch_size=[32],
        )
    else:  # twitter
        hyperparameters = dict(
            dataset=['twitter'],
            K=[3],
            predict_time=[3600*24*32],
            observation_time=[1*24*3600],
            time_interval=[900],
            gnn_hidden_size=[128],
            snn_hidden_size=[64],
            num_gnn_layers=[2],
            num_snn_layers=[2],
            num_attention_heads=[4],
            mlp=[[128, 64, 1]],
            lr=[0.0005],
            batch_size=[32],
        )
    
    # Run experiments
    for params in product(*list(hyperparameters.values())):
        (dataset, K, pred_time, obs_time, time_interval, 
         gnn_hidden, snn_hidden, gnn_layers, snn_layers, 
         attention_heads, mlp, lr, batch_size) = params
        
        # Create configuration using presets or manual config
        if 'MANUAL_CONFIG' in globals() and MANUAL_CONFIG:
            config = Config(
                dataset=dataset, pred_time=pred_time, observation_time=obs_time,
                time_interval=time_interval, gnn_hidden_size=gnn_hidden,
                snn_hidden_size=snn_hidden, num_gnn_layers=gnn_layers,
                num_snn_layers=snn_layers, num_attention_heads=attention_heads,
                mlp=mlp, lr=lr, batch_size=batch_size, epochs=100,
                **MANUAL_CONFIG
            )
        else:
            config = create_config_from_presets(
                dataset, pred_time, obs_time, time_interval,
                gnn_preset=GNN_PRESET, snn_preset=SNN_PRESET,
                gnn_hidden_size=gnn_hidden, snn_hidden_size=snn_hidden,
                num_gnn_layers=gnn_layers, num_snn_layers=snn_layers,
                num_attention_heads=attention_heads, mlp=mlp, lr=lr,
                batch_size=batch_size, epochs=100
            )
        
        print(f"\nConfiguration: {config.toString()}")
        
        # Load and preprocess data
        print("Loading data...")
        data_start = time.time()
        try:
            X, Y = load_cascade_data(config)
            X, Y = preprocess_data(X, Y, config)
        except Exception as e:
            print(f"Data loading failed: {e}")
            continue
        
        data_time = int(time.time() - data_start)
        print(f"Data loading completed in {data_time}s")
        
        # Run multiple experiments
        results = []
        for run in range(K):
            print(f"\n--- Run {run + 1}/{K} ---")
            
            train_start = time.time()
            
            try:
                # Create and train model
                model = MyCas(config)
                train_msle, val_msle, test_msle, test_mape = model.train_model(X, Y)
                
                train_time = int(time.time() - train_start)
                
                result = {
                    'train_msle': train_msle,
                    'val_msle': val_msle,
                    'test_msle': test_msle,
                    'test_mape': test_mape,
                    'train_time': train_time
                }
                results.append(result)
                
                print(f"Results: Train={train_msle:.4f}, Val={val_msle:.4f}, "
                      f"Test={test_msle:.4f}/{test_mape:.4f}, Time={train_time}s")
                
                # Save individual result
                result_str = (f"{train_msle:.3f}/{val_msle:.3f}/{test_msle:.3f}-{test_mape:.3f} "
                             f"P/T:{data_time}/{train_time} {config.toString()}\n")
                save_result(f"mycas/{dataset}_results.txt", result_str)
                
            except Exception as e:
                print(f"Training failed in run {run + 1}: {e}")
                continue
        
        # Calculate and save average results
        if results:
            avg_results = {
                'train_msle': sum(r['train_msle'] for r in results) / len(results),
                'val_msle': sum(r['val_msle'] for r in results) / len(results),
                'test_msle': sum(r['test_msle'] for r in results) / len(results),
                'test_mape': sum(r['test_mape'] for r in results) / len(results),
                'train_time': sum(r['train_time'] for r in results) / len(results)
            }
            
            avg_str = (f"AVG: {avg_results['train_msle']:.3f}/{avg_results['val_msle']:.3f}/"
                      f"{avg_results['test_msle']:.3f}-{avg_results['test_mape']:.3f} "
                      f"Time:{avg_results['train_time']:.1f}s {config.toString()}\n\n")
            save_result(f"mycas/{dataset}_results.txt", avg_str)
            
            print(f"\nAverage Results: {avg_str.strip()}")

if __name__ == '__main__':
    main()
