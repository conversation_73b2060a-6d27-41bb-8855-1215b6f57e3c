import torch
import torch.nn as nn

class Loss(nn.Module):
    def __init__(self, loss_type):
        super(Loss, self).__init__()
        self.loss_type = loss_type

    def forward(self, input_tensor, target):
        if self.loss_type == 'mse':
            return (input_tensor - target).pow(2).mean()
        
        elif self.loss_type == 'msle':
            input_log = input_tensor.log2()
            target_log = target.log2()
            return (input_log - target_log).pow(2).mean()
        
        elif self.loss_type == 'mape':
            return (input_tensor.add(1).log2() - target.add(1).log2()).abs().div(
                target.add(2).log2()).mean()
        
        elif self.loss_type == 'huber':
            return nn.SmoothL1Loss()(input_tensor, target)
        
        elif self.loss_type == 'focal_mse':
            # Focal loss variant for MSE
            mse = (input_tensor - target).pow(2)
            alpha = 0.25
            gamma = 2.0
            focal_weight = alpha * (1 - torch.exp(-mse)).pow(gamma)
            return (focal_weight * mse).mean()
        
        return None

class CombinedLoss(nn.Module):
    def __init__(self, weights={'mse': 0.5, 'msle': 0.3, 'mape': 0.2}):
        super(CombinedLoss, self).__init__()
        self.weights = weights
        self.losses = {name: Loss(name) for name in weights.keys()}
    
    def forward(self, input_tensor, target):
        total_loss = 0
        for name, weight in self.weights.items():
            if name in self.losses:
                loss_val = self.losses[name](input_tensor, target)
                total_loss += weight * loss_val
        return total_loss

class SpikingRegularizationLoss(nn.Module):
    def __init__(self, spike_reg_weight=0.01):
        super(SpikingRegularizationLoss, self).__init__()
        self.spike_reg_weight = spike_reg_weight
    
    def forward(self, spike_activity):
        # Encourage sparse spiking
        spike_rate = torch.mean(spike_activity)
        reg_loss = self.spike_reg_weight * spike_rate
        return reg_loss