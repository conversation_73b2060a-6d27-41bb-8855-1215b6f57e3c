import torch
import torch.nn as nn
import torch.nn.functional as F
import math

# ============ Basic Neuron Models ============

class LIFNeuron(nn.Module):
    def __init__(self, threshold=1.0, decay=0.9, reset_mode='zero'):
        super(LIFNeuron, self).__init__()
        self.threshold = threshold
        self.decay = decay
        self.reset_mode = reset_mode
        
    def forward(self, x, membrane=None):
        if membrane is None:
            membrane = torch.zeros_like(x)
            
        # Leaky integration
        membrane = self.decay * membrane + x
        
        # Spike generation
        spike = (membrane >= self.threshold).float()
        
        # Reset membrane potential
        if self.reset_mode == 'zero':
            membrane = membrane * (1 - spike)
        elif self.reset_mode == 'subtract':
            membrane = membrane - spike * self.threshold
            
        return spike, membrane

class IFNeuron(nn.Module):
    """Integrate-and-Fire Neuron (from ref/spike/neuron.py)"""
    def __init__(self, threshold=1.0, reset=0.0, alpha=1.0):
        super(IFNeuron, self).__init__()
        self.threshold = threshold
        self.reset = reset
        self.alpha = alpha
        self.v = 0.0
        
    def forward(self, x, membrane=None):
        if membrane is None:
            membrane = torch.zeros_like(x)
            
        # Charge
        membrane = membrane + x
        
        # Fire
        spike = (membrane >= self.threshold).float()
        
        # Reset
        membrane = (1 - spike) * membrane + spike * self.reset
        
        return spike, membrane

class AdaptiveThresholdNeuron(nn.Module):
    """Neuron with adaptive threshold"""
    def __init__(self, initial_threshold=1.0, decay=0.9, thresh_decay=0.7, gamma=0.2):
        super(AdaptiveThresholdNeuron, self).__init__()
        self.initial_threshold = initial_threshold
        self.decay = decay
        self.thresh_decay = thresh_decay
        self.gamma = gamma
        
    def forward(self, x, state=None):
        if state is None:
            membrane = torch.zeros_like(x)
            threshold = torch.full_like(x, self.initial_threshold)
        else:
            membrane, threshold = state
            
        # Leaky integration
        membrane = self.decay * membrane + x
        
        # Spike generation with adaptive threshold
        spike = (membrane >= threshold).float()
        
        # Reset membrane
        membrane = membrane * (1 - spike)
        
        # Update threshold
        threshold = self.gamma * spike + threshold * self.thresh_decay
        
        return spike, (membrane, threshold)

# ============ Surrogate Gradient Functions ============

class SurrogateGradient(torch.autograd.Function):
    @staticmethod
    def forward(ctx, input, threshold):
        ctx.save_for_backward(input)
        ctx.threshold = threshold
        return (input >= threshold).float()
    
    @staticmethod
    def backward(ctx, grad_output):
        input, = ctx.saved_tensors
        # Surrogate gradient: derivative of sigmoid
        grad_input = grad_output * torch.sigmoid(input) * (1 - torch.sigmoid(input))
        return grad_input, None

class SuperSpike(torch.autograd.Function):
    """SuperSpike surrogate gradient (from ref/spike/neuron.py)"""
    @staticmethod
    def forward(ctx, x, alpha):
        ctx.save_for_backward(x, alpha)
        return x.gt(0).float()
    
    @staticmethod
    def backward(ctx, grad_output):
        x, alpha = ctx.saved_tensors
        grad_input = grad_output.clone()
        sg = 1.0 / (1.0 + (alpha * x).abs()) ** 2
        return grad_input * sg, None

class MultiGaussSpike(torch.autograd.Function):
    """Multi-Gaussian surrogate gradient (from ref/spike/neuron.py)"""
    @staticmethod
    def forward(ctx, x, alpha):
        ctx.save_for_backward(x, alpha)
        return x.gt(0).float()
    
    @staticmethod
    def backward(ctx, grad_output):
        x, alpha = ctx.saved_tensors
        grad_input = grad_output.clone()
        zero = torch.tensor(0.0)
        pi = torch.tensor(math.pi)
        
        def gaussian(x, mu, sigma):
            return torch.exp(-((x - mu) * (x - mu)) / (2 * sigma * sigma)) / (sigma * torch.sqrt(2 * pi))
        
        sg = (1.15 * gaussian(x, zero, alpha) - 
              0.15 * gaussian(x, alpha, 6 * alpha) - 
              0.15 * gaussian(x, -alpha, 6 * alpha))
        return grad_input * sg, None

class TriangleSpike(torch.autograd.Function):
    """Triangle surrogate gradient (from ref/spike/neuron.py)"""
    @staticmethod
    def forward(ctx, x, alpha):
        ctx.save_for_backward(x, alpha)
        return x.gt(0).float()
    
    @staticmethod
    def backward(ctx, grad_output):
        x, alpha = ctx.saved_tensors
        grad_input = grad_output.clone()
        sg = torch.clamp(1 - torch.abs(x) / alpha, min=0)
        return grad_input * sg, None

# ============ Spiking Layers ============

class SpikingLayer(nn.Module):
    def __init__(self, in_features, out_features, neuron_type='lif', 
                 surrogate_type='sigmoid', threshold=1.0, decay=0.9, **kwargs):
        super(SpikingLayer, self).__init__()
        self.linear = nn.Linear(in_features, out_features)
        self.neuron_type = neuron_type
        self.surrogate_type = surrogate_type
        self.threshold = threshold
        
        # Create neuron based on type - CONFIGURABLE HERE
        self.neuron = self._create_neuron(neuron_type, threshold, decay, **kwargs)
        
        # Surrogate gradient function - CONFIGURABLE HERE
        self.surrogate_fn = self._get_surrogate_function(surrogate_type)
        
    def _create_neuron(self, neuron_type, threshold, decay, **kwargs):
        """Create neuron based on type - MAIN CONFIGURATION POINT"""
        if neuron_type == 'lif':
            return LIFNeuron(threshold, decay, kwargs.get('reset_mode', 'zero'))
        elif neuron_type == 'if':
            return IFNeuron(threshold, kwargs.get('reset', 0.0), kwargs.get('alpha', 1.0))
        elif neuron_type == 'adaptive':
            return AdaptiveThresholdNeuron(threshold, decay, 
                                         kwargs.get('thresh_decay', 0.7),
                                         kwargs.get('gamma', 0.2))
        else:
            raise ValueError(f"Unknown neuron type: {neuron_type}")
    
    def _get_surrogate_function(self, surrogate_type):
        """Get surrogate gradient function - CONFIGURABLE HERE"""
        if surrogate_type == 'sigmoid':
            return SurrogateGradient.apply
        elif surrogate_type == 'superspike':
            return SuperSpike.apply
        elif surrogate_type == 'multigauss':
            return MultiGaussSpike.apply
        elif surrogate_type == 'triangle':
            return TriangleSpike.apply
        else:
            return SurrogateGradient.apply  # default
        
    def forward(self, x, state=None):
        x = self.linear(x)
        
        if self.neuron_type == 'adaptive':
            spike, new_state = self.neuron(x, state)
        else:
            spike, membrane = self.neuron(x, state)
            new_state = membrane
            
        # Use surrogate gradient for backpropagation
        if self.surrogate_type in ['superspike', 'multigauss', 'triangle']:
            alpha = torch.tensor(1.0, device=x.device)
            spike = self.surrogate_fn(x - self.threshold, alpha)
        else:
            spike = self.surrogate_fn(x, self.threshold)
            
        return spike, new_state

class SpikingEncoder(nn.Module):
    def __init__(self, config):
        super(SpikingEncoder, self).__init__()
        self.config = config
        
        # Input encoding layer - CONFIGURABLE HERE
        self.input_layer = SpikingLayer(
            config.gnn_hidden_size, config.snn_hidden_size,
            neuron_type=config.snn_neuron_type,
            surrogate_type=config.snn_surrogate_type,
            threshold=config.spike_threshold, 
            decay=config.membrane_decay
        )
        
        # Hidden spiking layers - CONFIGURABLE HERE
        self.hidden_layers = nn.ModuleList()
        for _ in range(config.num_snn_layers - 1):
            self.hidden_layers.append(
                SpikingLayer(
                    config.snn_hidden_size, config.snn_hidden_size,
                    neuron_type=config.snn_neuron_type,
                    surrogate_type=config.snn_surrogate_type,
                    threshold=config.spike_threshold,
                    decay=config.membrane_decay
                )
            )
        
        # Temporal integration - CONFIGURABLE HERE
        if config.snn_temporal_integration == 'weighted':
            self.temporal_weight = nn.Parameter(torch.ones(config.spike_timesteps))
        elif config.snn_temporal_integration == 'attention':
            self.temporal_attention = nn.MultiheadAttention(
                config.snn_hidden_size, 4, batch_first=True
            )
        
    def forward(self, x):
        batch_size = x.size(0)
        device = x.device
        
        # Initialize states
        states = [None] * (len(self.hidden_layers) + 1)
        
        # Collect spikes over time
        spike_trains = []
        
        for t in range(self.config.spike_timesteps):
            # Add temporal noise for spike generation
            noise_scale = getattr(self.config, 'snn_noise_scale', 0.1)
            x_t = x + noise_scale * torch.randn_like(x)
            
            # Forward through spiking layers
            spike, states[0] = self.input_layer(x_t, states[0])
            
            for i, layer in enumerate(self.hidden_layers):
                spike, states[i+1] = layer(spike, states[i+1])
            
            spike_trains.append(spike)
        
        # Temporal integration - CONFIGURABLE HERE
        spike_trains = torch.stack(spike_trains, dim=1)  # [batch, time, features]
        
        if self.config.snn_temporal_integration == 'weighted':
            weights = F.softmax(self.temporal_weight, dim=0)
            integrated_spikes = torch.sum(spike_trains * weights.view(1, -1, 1), dim=1)
        elif self.config.snn_temporal_integration == 'attention':
            integrated_spikes, _ = self.temporal_attention(spike_trains, spike_trains, spike_trains)
            integrated_spikes = torch.mean(integrated_spikes, dim=1)
        elif self.config.snn_temporal_integration == 'mean':
            integrated_spikes = torch.mean(spike_trains, dim=1)
        elif self.config.snn_temporal_integration == 'sum':
            integrated_spikes = torch.sum(spike_trains, dim=1)
        else:
            # Default: weighted
            weights = F.softmax(torch.ones(self.config.spike_timesteps), dim=0)
            integrated_spikes = torch.sum(spike_trains * weights.view(1, -1, 1), dim=1)
        
        return integrated_spikes

class AdaptiveSpikingLayer(nn.Module):
    def __init__(self, in_features, out_features, initial_threshold=1.0):
        super(AdaptiveSpikingLayer, self).__init__()
        self.linear = nn.Linear(in_features, out_features)
        self.threshold = nn.Parameter(torch.full((out_features,), initial_threshold))
        self.decay = nn.Parameter(torch.full((out_features,), 0.9))
        
    def forward(self, x, membrane=None):
        if membrane is None:
            membrane = torch.zeros(x.size(0), self.linear.out_features, device=x.device)
            
        x = self.linear(x)
        membrane = self.decay * membrane + x
        
        # Adaptive threshold
        spike = (membrane >= self.threshold).float()
        membrane = membrane * (1 - spike)
        
        return spike, membrane

# ============ Advanced SNN Architectures ============

class RecurrentSpikingLayer(nn.Module):
    """Spiking layer with recurrent connections"""
    def __init__(self, in_features, hidden_features, neuron_type='lif', **kwargs):
        super(RecurrentSpikingLayer, self).__init__()
        self.hidden_features = hidden_features
        
        self.input_layer = SpikingLayer(in_features, hidden_features, neuron_type, **kwargs)
        self.recurrent_layer = SpikingLayer(hidden_features, hidden_features, neuron_type, **kwargs)
        
    def forward(self, x, hidden_state=None):
        if hidden_state is None:
            hidden_spike = torch.zeros(x.size(0), self.hidden_features, device=x.device)
            hidden_membrane = None
        else:
            hidden_spike, hidden_membrane = hidden_state
            
        # Input processing
        input_spike, input_membrane = self.input_layer(x)
        
        # Recurrent processing
        recurrent_spike, hidden_membrane = self.recurrent_layer(hidden_spike, hidden_membrane)
        
        # Combine input and recurrent spikes
        output_spike = input_spike + recurrent_spike
        
        return output_spike, (output_spike, hidden_membrane)
