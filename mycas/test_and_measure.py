import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import json
import os
import time
from datetime import datetime
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from scipy.stats import pearsonr, spearmanr
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import pickle

from model import MyCas
from config import Config
from data_processor import load_cascade_data, preprocess_data

class CascadeMetrics:
    """Comprehensive metrics for cascade prediction evaluation"""
    
    @staticmethod
    def calculate_all_metrics(y_true, y_pred):
        """Calculate all cascade prediction metrics"""
        y_true = np.array(y_true).flatten()
        y_pred = np.array(y_pred).flatten()
        
        # Remove invalid values
        mask = ~(np.isnan(y_true) | np.isnan(y_pred) | np.isinf(y_true) | np.isinf(y_pred))
        y_true = y_true[mask]
        y_pred = y_pred[mask]
        
        if len(y_true) == 0:
            return {metric: float('inf') for metric in ['mae', 'mse', 'rmse', 'msle', 'mape', 'smape', 'r2', 'pearson', 'spearman']}
        
        metrics = {}
        
        # Basic regression metrics
        metrics['mae'] = mean_absolute_error(y_true, y_pred)
        metrics['mse'] = mean_squared_error(y_true, y_pred)
        metrics['rmse'] = np.sqrt(metrics['mse'])
        
        # MSLE (Mean Squared Logarithmic Error)
        y_true_log = np.log1p(np.maximum(y_true, 0))
        y_pred_log = np.log1p(np.maximum(y_pred, 0))
        metrics['msle'] = mean_squared_error(y_true_log, y_pred_log)
        
        # MAPE (Mean Absolute Percentage Error)
        epsilon = 1e-8
        metrics['mape'] = np.mean(np.abs((y_true - y_pred) / (y_true + epsilon))) * 100
        
        # SMAPE (Symmetric Mean Absolute Percentage Error)
        metrics['smape'] = np.mean(2 * np.abs(y_true - y_pred) / (np.abs(y_true) + np.abs(y_pred) + epsilon)) * 100
        
        # R-squared
        metrics['r2'] = r2_score(y_true, y_pred)
        
        # Correlation metrics
        if len(np.unique(y_true)) > 1 and len(np.unique(y_pred)) > 1:
            metrics['pearson'], _ = pearsonr(y_true, y_pred)
            metrics['spearman'], _ = spearmanr(y_true, y_pred)
        else:
            metrics['pearson'] = 0.0
            metrics['spearman'] = 0.0
        
        # Cascade-specific metrics
        metrics.update(CascadeMetrics._cascade_specific_metrics(y_true, y_pred))
        
        return metrics
    
    @staticmethod
    def _cascade_specific_metrics(y_true, y_pred):
        """Calculate cascade-specific metrics"""
        metrics = {}
        
        # Prediction accuracy for different cascade sizes
        small_mask = y_true <= np.percentile(y_true, 33)
        medium_mask = (y_true > np.percentile(y_true, 33)) & (y_true <= np.percentile(y_true, 67))
        large_mask = y_true > np.percentile(y_true, 67)
        
        for size, mask in [('small', small_mask), ('medium', medium_mask), ('large', large_mask)]:
            if np.sum(mask) > 0:
                metrics[f'mae_{size}'] = mean_absolute_error(y_true[mask], y_pred[mask])
                metrics[f'mape_{size}'] = np.mean(np.abs((y_true[mask] - y_pred[mask]) / (y_true[mask] + 1e-8))) * 100
            else:
                metrics[f'mae_{size}'] = float('inf')
                metrics[f'mape_{size}'] = float('inf')
        
        # Hit rate (percentage of predictions within certain error bounds)
        for threshold in [0.1, 0.2, 0.5]:
            relative_error = np.abs(y_true - y_pred) / (y_true + 1e-8)
            metrics[f'hit_rate_{int(threshold*100)}'] = np.mean(relative_error <= threshold) * 100
        
        # Trend prediction accuracy
        if len(y_true) > 1:
            true_trend = np.diff(y_true) > 0
            pred_trend = np.diff(y_pred) > 0
            metrics['trend_accuracy'] = np.mean(true_trend == pred_trend) * 100
        else:
            metrics['trend_accuracy'] = 0.0
        
        return metrics

class ModelManager:
    """Manage model weights and training state"""
    
    def __init__(self, base_dir="mycas/experiments"):
        self.base_dir = base_dir
        os.makedirs(base_dir, exist_ok=True)
        
    def save_model_state(self, model, config, metrics, epoch, is_best=False):
        """Save model state with metadata"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_id = f"{config.dataset}_{config.gnn_layer_type}_{config.snn_neuron_type}_{timestamp}"
        
        model_dir = os.path.join(self.base_dir, model_id)
        os.makedirs(model_dir, exist_ok=True)
        
        # Save model weights
        model_path = os.path.join(model_dir, f"model_epoch_{epoch}.pth")
        torch.save({
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': model.optimizer.state_dict(),
            'scheduler_state_dict': model.scheduler.state_dict(),
            'epoch': epoch,
            'metrics': metrics,
            'config': config.__dict__
        }, model_path)
        
        # Save best model separately
        if is_best:
            best_path = os.path.join(model_dir, "best_model.pth")
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': model.optimizer.state_dict(),
                'scheduler_state_dict': model.scheduler.state_dict(),
                'epoch': epoch,
                'metrics': metrics,
                'config': config.__dict__
            }, best_path)
        
        # Save metadata
        metadata = {
            'model_id': model_id,
            'timestamp': timestamp,
            'epoch': epoch,
            'metrics': metrics,
            'config': config.__dict__,
            'model_path': model_path,
            'is_best': is_best
        }
        
        metadata_path = os.path.join(model_dir, "metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        return model_id, model_path
    
    def load_model_state(self, model_path, model, device='cuda'):
        """Load model state from checkpoint"""
        checkpoint = torch.load(model_path, map_location=device)
        
        model.load_state_dict(checkpoint['model_state_dict'])
        model.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        model.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        return checkpoint['epoch'], checkpoint['metrics']
    
    def list_models(self, dataset=None, gnn_type=None, snn_type=None):
        """List available models with filtering"""
        models = []
        
        for model_dir in os.listdir(self.base_dir):
            metadata_path = os.path.join(self.base_dir, model_dir, "metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                # Apply filters
                if dataset and metadata['config']['dataset'] != dataset:
                    continue
                if gnn_type and metadata['config']['gnn_layer_type'] != gnn_type:
                    continue
                if snn_type and metadata['config']['snn_neuron_type'] != snn_type:
                    continue
                
                models.append(metadata)
        
        return sorted(models, key=lambda x: x['timestamp'], reverse=True)

class CascadeTestSuite:
    """Comprehensive testing suite for cascade prediction models"""
    
    def __init__(self, base_dir="mycas/experiments"):
        self.metrics_calculator = CascadeMetrics()
        self.model_manager = ModelManager(base_dir)
        self.results = defaultdict(list)
        
    def test_model_configurations(self, dataset, pred_time, obs_time, time_interval, 
                                gnn_types=None, snn_types=None, epochs=50, runs=3):
        """Test different model configurations"""
        
        if gnn_types is None:
            gnn_types = ['gat', 'gcn', 'sage', 'gin']
        
        if snn_types is None:
            snn_types = [
                {'neuron': 'lif', 'surrogate': 'sigmoid'},
                {'neuron': 'lif', 'surrogate': 'superspike'},
                {'neuron': 'if', 'surrogate': 'triangle'},
                {'neuron': 'adaptive', 'surrogate': 'multigauss'}
            ]
        
        print(f"Testing {len(gnn_types)} GNN types × {len(snn_types)} SNN configs × {runs} runs")
        print("=" * 80)
        
        # Load data once
        base_config = Config(dataset, pred_time, obs_time, time_interval, epochs=epochs)
        X, Y = load_cascade_data(base_config)
        X, Y = preprocess_data(X, Y, base_config)
        
        total_configs = len(gnn_types) * len(snn_types)
        current_config = 0
        
        for gnn_type in gnn_types:
            for snn_config in snn_types:
                current_config += 1
                config_name = f"{gnn_type}+{snn_config['neuron']}_{snn_config['surrogate']}"
                
                print(f"\n[{current_config}/{total_configs}] Testing: {config_name}")
                print("-" * 60)
                
                config_results = []
                
                for run in range(runs):
                    print(f"Run {run + 1}/{runs}...")
                    
                    try:
                        # Create config
                        config = Config(
                            dataset, pred_time, obs_time, time_interval,
                            gnn_layer_type=gnn_type,
                            snn_neuron_type=snn_config['neuron'],
                            snn_surrogate_type=snn_config['surrogate'],
                            epochs=epochs,
                            batch_size=16
                        )
                        
                        # Train and evaluate model
                        results = self._train_and_evaluate_model(config, X, Y, run)
                        config_results.append(results)
                        
                        print(f"✓ Run {run + 1}: Test MSLE={results['test_metrics']['msle']:.4f}, "
                              f"MAPE={results['test_metrics']['mape']:.2f}%")
                        
                    except Exception as e:
                        print(f"✗ Run {run + 1}: Failed - {e}")
                        continue
                
                # Aggregate results for this configuration
                if config_results:
                    aggregated = self._aggregate_results(config_results, config_name)
                    self.results[config_name] = aggregated
                    
                    print(f"Average: MSLE={aggregated['avg_test_msle']:.4f}±{aggregated['std_test_msle']:.4f}, "
                          f"MAPE={aggregated['avg_test_mape']:.2f}±{aggregated['std_test_mape']:.2f}%")
        
        # Save comprehensive results
        self._save_comprehensive_results(dataset)
        self._generate_analysis_report(dataset)
        
        return self.results
    
    def _train_and_evaluate_model(self, config, X, Y, run_id):
        """Train and evaluate a single model"""
        start_time = time.time()
        
        # Create model
        model = MyCas(config)
        
        # Enhanced training with detailed tracking
        train_metrics, val_metrics, test_metrics, model_id = self._enhanced_training(
            model, config, X, Y, run_id
        )
        
        training_time = time.time() - start_time
        
        return {
            'config': config.__dict__,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'test_metrics': test_metrics,
            'training_time': training_time,
            'model_id': model_id,
            'run_id': run_id
        }
    
    def _enhanced_training(self, model, config, X, Y, run_id):
        """Enhanced training with comprehensive metrics tracking"""
        from sklearn.model_selection import train_test_split
        from torch.utils.data import DataLoader, TensorDataset
        
        # Data splitting
        X_temp, X_test, Y_temp, Y_test = train_test_split(X, Y, test_size=0.2, random_state=42)
        X_train, X_val, Y_train, Y_val = train_test_split(X_temp, Y_temp, test_size=0.25, random_state=42)
        
        # Convert to tensors
        datasets = {}
        for name, (x, y) in [('train', (X_train, Y_train)), ('val', (X_val, Y_val)), ('test', (X_test, Y_test))]:
            x_tensor = torch.FloatTensor(x)
            y_tensor = torch.FloatTensor(y)
            dataset = TensorDataset(x_tensor, y_tensor)
            datasets[name] = DataLoader(dataset, batch_size=config.batch_size, 
                                      shuffle=(name=='train'), num_workers=2, pin_memory=True)
        
        # Training setup
        criterion = nn.MSELoss()
        best_val_loss = float('inf')
        patience_counter = 0
        patience = 15
        
        epoch_metrics = []
        best_metrics = None
        model_id = None
        
        for epoch in range(config.epochs):
            # Training phase
            model.train()
            train_losses = []
            train_predictions = []
            train_targets = []
            
            for batch_x, batch_y in datasets['train']:
                if torch.cuda.is_available():
                    batch_x, batch_y = batch_x.cuda(), batch_y.cuda()
                
                model.optimizer.zero_grad()
                predictions = model(batch_x)
                loss = criterion(predictions, batch_y)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                model.optimizer.step()
                
                train_losses.append(loss.item())
                train_predictions.extend(predictions.detach().cpu().numpy())
                train_targets.extend(batch_y.detach().cpu().numpy())
            
            # Validation phase
            model.eval()
            val_losses = []
            val_predictions = []
            val_targets = []
            
            with torch.no_grad():
                for batch_x, batch_y in datasets['val']:
                    if torch.cuda.is_available():
                        batch_x, batch_y = batch_x.cuda(), batch_y.cuda()
                    
                    predictions = model(batch_x)
                    loss = criterion(predictions, batch_y)
                    
                    val_losses.append(loss.item())
                    val_predictions.extend(predictions.cpu().numpy())
                    val_targets.extend(batch_y.cpu().numpy())
            
            # Test evaluation
            test_predictions = []
            test_targets = []
            
            with torch.no_grad():
                for batch_x, batch_y in datasets['test']:
                    if torch.cuda.is_available():
                        batch_x, batch_y = batch_x.cuda(), batch_y.cuda()
                    
                    predictions = model(batch_x)
                    test_predictions.extend(predictions.cpu().numpy())
                    test_targets.extend(batch_y.cpu().numpy())
            
            # Calculate comprehensive metrics
            train_metrics = self.metrics_calculator.calculate_all_metrics(train_targets, train_predictions)
            val_metrics = self.metrics_calculator.calculate_all_metrics(val_targets, val_predictions)
            test_metrics = self.metrics_calculator.calculate_all_metrics(test_targets, test_predictions)
            
            # Learning rate scheduling
            model.scheduler.step()
            
            # Track epoch metrics
            epoch_data = {
                'epoch': epoch,
                'train_metrics': train_metrics,
                'val_metrics': val_metrics,
                'test_metrics': test_metrics,
                'lr': model.optimizer.param_groups[0]['lr']
            }
            epoch_metrics.append(epoch_data)
            
            # Early stopping and model saving
            val_loss = val_metrics['msle']
            is_best = val_loss < best_val_loss
            
            if is_best:
                best_val_loss = val_loss
                patience_counter = 0
                best_metrics = {
                    'train': train_metrics,
                    'val': val_metrics,
                    'test': test_metrics
                }
                
                # Save best model
                model_id, _ = self.model_manager.save_model_state(
                    model, config, test_metrics, epoch, is_best=True
                )
            else:
                patience_counter += 1
            
            # Save current model periodically
            if epoch % 10 == 0:
                self.model_manager.save_model_state(
                    model, config, test_metrics, epoch, is_best=False
                )
            
            # Print progress
            if epoch % 10 == 0 or is_best:
                print(f"Epoch {epoch+1}: Train MSLE={train_metrics['msle']:.4f}, "
                      f"Val MSLE={val_metrics['msle']:.4f}, Test MSLE={test_metrics['msle']:.4f}")
            
            # Early stopping
            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch+1}")
                break
        
        return best_metrics['train'], best_metrics['val'], best_metrics['test'], model_id
    
    def _aggregate_results(self, config_results, config_name):
        """Aggregate results across multiple runs"""
        if not config_results:
            return {}
        
        # Extract metrics from all runs
        train_metrics = [r['train_metrics'] for r in config_results]
        val_metrics = [r['val_metrics'] for r in config_results]
        test_metrics = [r['test_metrics'] for r in config_results]
        training_times = [r['training_time'] for r in config_results]
        
        aggregated = {
            'config_name': config_name,
            'num_runs': len(config_results),
            'avg_training_time': np.mean(training_times),
            'std_training_time': np.std(training_times)
        }
        
        # Aggregate each metric
        for phase, metrics_list in [('train', train_metrics), ('val', val_metrics), ('test', test_metrics)]:
            for metric_name in metrics_list[0].keys():
                values = [m[metric_name] for m in metrics_list if not np.isnan(m[metric_name]) and not np.isinf(m[metric_name])]
                if values:
                    aggregated[f'avg_{phase}_{metric_name}'] = np.mean(values)
                    aggregated[f'std_{phase}_{metric_name}'] = np.std(values)
                    aggregated[f'min_{phase}_{metric_name}'] = np.min(values)
                    aggregated[f'max_{phase}_{metric_name}'] = np.max(values)
                else:
                    aggregated[f'avg_{phase}_{metric_name}'] = float('inf')
                    aggregated[f'std_{phase}_{metric_name}'] = float('inf')
        
        return aggregated
    
    def _save_comprehensive_results(self, dataset):
        """Save comprehensive results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = os.path.join(self.model_manager.base_dir, f"results_{dataset}_{timestamp}")
        os.makedirs(results_dir, exist_ok=True)
        
        # Save detailed results as JSON
        results_file = os.path.join(results_dir, "detailed_results.json")
        with open(results_file, 'w') as f:
            json.dump(dict(self.results), f, indent=2, default=str)
        
        # Save summary as CSV
        summary_data = []
        for config_name, results in self.results.items():
            summary_data.append({
                'Configuration': config_name,
                'Test_MSLE_Mean': results.get('avg_test_msle', float('inf')),
                'Test_MSLE_Std': results.get('std_test_msle', float('inf')),
                'Test_MAPE_Mean': results.get('avg_test_mape', float('inf')),
                'Test_MAPE_Std': results.get('std_test_mape', float('inf')),
                'Test_MAE_Mean': results.get('avg_test_mae', float('inf')),
                'Test_R2_Mean': results.get('avg_test_r2', -float('inf')),
                'Training_Time_Mean': results.get('avg_training_time', float('inf')),
                'Num_Runs': results.get('num_runs', 0)
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('Test_MSLE_Mean')
        summary_file = os.path.join(results_dir, "summary_results.csv")
        summary_df.to_csv(summary_file, index=False)
        
        print(f"\nResults saved to: {results_dir}")
        print(f"Summary: {summary_file}")
        print(f"Detailed: {results_file}")
        
        return results_dir
    
    def _generate_analysis_report(self, dataset):
        """Generate analysis report with visualizations"""
        if not self.results:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_dir = os.path.join(self.model_manager.base_dir, f"analysis_{dataset}_{timestamp}")
        os.makedirs(report_dir, exist_ok=True)
        
        # Performance comparison plot
        self._plot_performance_comparison(report_dir)
        
        # Metrics distribution plot
        self._plot_metrics_distribution(report_dir)
        
        # Generate text report
        self._generate_text_report(report_dir, dataset)
        
        print(f"Analysis report generated: {report_dir}")
    
    def _plot_performance_comparison(self, report_dir):
        """Plot performance comparison across configurations"""
        configs = list(self.results.keys())
        msle_means = [self.results[c].get('avg_test_msle', float('inf')) for c in configs]
        msle_stds = [self.results[c].get('std_test_msle', 0) for c in configs]
        
        plt.figure(figsize=(12, 8))
        plt.errorbar(range(len(configs)), msle_means, yerr=msle_stds, 
                    fmt='o', capsize=5, capthick=2, markersize=8)
        plt.xticks(range(len(configs)), configs, rotation=45, ha='right')
        plt.ylabel('Test MSLE')
        plt.title('Model Performance Comparison (Test MSLE)')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(report_dir, 'performance_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_metrics_distribution(self, report_dir):
        """Plot distribution of different metrics"""
        metrics_to_plot = ['msle', 'mape', 'mae', 'r2']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        for i, metric in enumerate(metrics_to_plot):
            values = []
            labels = []
            
            for config_name, results in self.results.items():
                val = results.get(f'avg_test_{metric}', None)
                if val is not None and not np.isinf(val) and not np.isnan(val):
                    values.append(val)
                    labels.append(config_name)
            
            if values:
                axes[i].bar(range(len(values)), values)
                axes[i].set_xticks(range(len(labels)))
                axes[i].set_xticklabels(labels, rotation=45, ha='right')
                axes[i].set_ylabel(metric.upper())
                axes[i].set_title(f'Test {metric.upper()} Comparison')
                axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(report_dir, 'metrics_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_text_report(self, report_dir, dataset):
        """Generate comprehensive text report"""
        report_file = os.path.join(report_dir, 'analysis_report.txt')
        
        with open(report_file, 'w') as f:
            f.write(f"Cascade Prediction Model Analysis Report\n")
            f.write(f"Dataset: {dataset}\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")
            
            # Best performing models
            sorted_results = sorted(self.results.items(), 
                                  key=lambda x: x[1].get('avg_test_msle', float('inf')))
            
            f.write("TOP 5 PERFORMING MODELS (by Test MSLE):\n")
            f.write("-" * 50 + "\n")
            for i, (config_name, results) in enumerate(sorted_results[:5], 1):
                f.write(f"{i}. {config_name}\n")
                f.write(f"   Test MSLE: {results.get('avg_test_msle', 'N/A'):.4f} ± {results.get('std_test_msle', 'N/A'):.4f}\n")
                f.write(f"   Test MAPE: {results.get('avg_test_mape', 'N/A'):.2f}% ± {results.get('std_test_mape', 'N/A'):.2f}%\n")
                f.write(f"   Test MAE:  {results.get('avg_test_mae', 'N/A'):.4f} ± {results.get('std_test_mae', 'N/A'):.4f}\n")
                f.write(f"   Test R²:   {results.get('avg_test_r2', 'N/A'):.4f} ± {results.get('std_test_r2', 'N/A'):.4f}\n")
                f.write(f"   Training Time: {results.get('avg_training_time', 'N/A'):.1f}s ± {results.get('std_training_time', 'N/A'):.1f}s\n\n")
            
            # Detailed analysis
            f.write("\nDETAILED ANALYSIS:\n")
            f.write("-" * 50 + "\n")
            
            # GNN type analysis
            gnn_performance = defaultdict(list)
            for config_name, results in self.results.items():
                gnn_type = config_name.split('+')[0]
                msle = results.get('avg_test_msle', float('inf'))
                if not np.isinf(msle):
                    gnn_performance[gnn_type].append(msle)
            
            f.write("GNN Layer Performance:\n")
            for gnn_type, msles in gnn_performance.items():
                f.write(f"  {gnn_type}: {np.mean(msles):.4f} ± {np.std(msles):.4f} (avg ± std)\n")
            
            f.write("\n")
            
            # SNN type analysis
            snn_performance = defaultdict(list)
            for config_name, results in self.results.items():
                snn_part = config_name.split('+')[1]
                msle = results.get('avg_test_msle', float('inf'))
                if not np.isinf(msle):
                    snn_performance[snn_part].append(msle)
            
            f.write("SNN Configuration Performance:\n")
            for snn_type, msles in snn_performance.items():
                f.write(f"  {snn_type}: {np.mean(msles):.4f} ± {np.std(msles):.4f} (avg ± std)\n")

def main():
    """Main testing function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Comprehensive Cascade Prediction Model Testing')
    parser.add_argument('dataset', choices=['twitter', 'weibo'], help='Dataset to use')
    parser.add_argument('--pred_time', type=int, default=24, help='Prediction time')
    parser.add_argument('--obs_time', type=int, default=2, help='Observation time')
    parser.add_argument('--time_interval', type=int, default=1, help='Time interval')
    parser.add_argument('--epochs', type=int, default=50, help='Training epochs')
    parser.add_argument('--runs', type=int, default=3, help='Number of runs per configuration')
    parser.add_argument('--gnn_types', nargs='+', default=['gat', 'gcn', 'sage'], help='GNN types to test')
    parser.add_argument('--resume', type=str, help='Resume from checkpoint directory')
    
    args = parser.parse_args()
    
    # SNN configurations to test
    snn_configs = [
        {'neuron': 'lif', 'surrogate': 'sigmoid'},
        {'neuron': 'lif', 'surrogate': 'superspike'},
        {'neuron': 'if', 'surrogate': 'triangle'},
        {'neuron': 'adaptive', 'surrogate': 'multigauss'}
    ]
    
    # Initialize test suite
    test_suite = CascadeTestSuite()
    
    print(f"Starting comprehensive testing for {args.dataset} dataset")
    print(f"Configurations: {len(args.gnn_types)} GNN × {len(snn_configs)} SNN × {args.runs} runs")
    print(f"Total experiments: {len(args.gnn_types) * len(snn_configs) * args.runs}")
    
    # Run comprehensive testing
    results = test_suite.test_model_configurations(
        dataset=args.dataset,
        pred_time=args.pred_time,
        obs_time=args.obs_time,
        time_interval=args.time_interval,
        gnn_types=args.gnn_types,
        snn_types=snn_configs,
        epochs=args.epochs,
        runs=args.runs
    )
    
    print("\n" + "="*80)
    print("TESTING COMPLETED!")
    print("="*80)
    
    # Print summary
    if results:
        best_config = min(results.items(), key=lambda x: x[1].get('avg_test_msle', float('inf')))
        print(f"Best Configuration: {best_config[0]}")
        print(f"Best Test MSLE: {best_config[1].get('avg_test_msle', 'N/A'):.4f}")
        print(f"Best Test MAPE: {best_config[1].get('avg_test_mape', 'N/A'):.2f}%")

if __name__ == "__main__":
    main()