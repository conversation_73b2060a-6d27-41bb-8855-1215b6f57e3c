#!/usr/bin/env python3
import os
import sys
import argparse
import torch
from model import MyCas
from config import Config
from data_processor import load_cascade_data, preprocess_data

def find_latest_checkpoint(dataset, model_pattern=None):
    """Find the latest checkpoint for a dataset"""
    checkpoint_dir = f"mycas/checkpoints/{dataset}"
    if not os.path.exists(checkpoint_dir):
        return None
    
    checkpoints = [f for f in os.listdir(checkpoint_dir) if f.endswith('_latest.pth')]
    if model_pattern:
        checkpoints = [f for f in checkpoints if model_pattern in f]
    
    if not checkpoints:
        return None
    
    # Sort by modification time, get latest
    checkpoints.sort(key=lambda x: os.path.getmtime(os.path.join(checkpoint_dir, x)), reverse=True)
    return os.path.join(checkpoint_dir, checkpoints[0])

def list_available_checkpoints(dataset=None):
    """List all available checkpoints"""
    base_dir = "mycas/checkpoints"
    if not os.path.exists(base_dir):
        print("No checkpoints found.")
        return
    
    datasets = [dataset] if dataset else os.listdir(base_dir)
    
    for ds in datasets:
        ds_dir = os.path.join(base_dir, ds)
        if not os.path.isdir(ds_dir):
            continue
        
        checkpoints = [f for f in os.listdir(ds_dir) if f.endswith('_latest.pth')]
        if checkpoints:
            print(f"\n📁 {ds.upper()} Dataset:")
            for cp in sorted(checkpoints):
                cp_path = os.path.join(ds_dir, cp)
                checkpoint = torch.load(cp_path, map_location='cpu')
                model_id = checkpoint.get('model_id', 'unknown')
                epoch = checkpoint.get('epoch', 0)
                val_loss = checkpoint.get('val_loss', 0)
                test_loss = checkpoint.get('test_loss', 0)
                
                print(f"  🔹 {model_id}")
                print(f"     Epoch: {epoch}, Val: {val_loss:.4f}, Test: {test_loss:.4f}")

def resume_training(checkpoint_path, additional_epochs=50):
    """Resume training from checkpoint"""
    if not os.path.exists(checkpoint_path):
        print(f"❌ Checkpoint not found: {checkpoint_path}")
        return
    
    # Load checkpoint to get config
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    config_dict = checkpoint['config']
    
    # Recreate config
    config = Config(**{k: v for k, v in config_dict.items() 
                      if k in Config.__init__.__code__.co_varnames})
    config.epochs = checkpoint['epoch'] + additional_epochs
    
    print(f"🔄 Resuming training:")
    print(f"   Model: {checkpoint.get('model_id', 'unknown')}")
    print(f"   Dataset: {config.dataset}")
    print(f"   From epoch: {checkpoint['epoch']}")
    print(f"   Additional epochs: {additional_epochs}")
    
    # Load data
    X, Y = load_cascade_data(config)
    X, Y = preprocess_data(X, Y, config)
    
    # Create model and resume training
    model = MyCas(config)
    model.model_id = checkpoint.get('model_id', model.model_id)
    
    train_msle, val_msle, test_msle, test_mape = model.train_model(X, Y, resume_from=checkpoint_path)
    
    print(f"\n✅ Training completed!")
    print(f"Final results: Test MSLE={test_msle:.4f}, MAPE={test_mape:.2f}%")

def main():
    parser = argparse.ArgumentParser(description='Resume MyCas Training')
    parser.add_argument('action', choices=['list', 'resume', 'auto'], 
                       help='Action: list checkpoints, resume specific, or auto-resume latest')
    parser.add_argument('--dataset', choices=['twitter', 'weibo'], 
                       help='Dataset name')
    parser.add_argument('--model_id', type=str, 
                       help='Specific model ID to resume')
    parser.add_argument('--epochs', type=int, default=50, 
                       help='Additional epochs to train')
    
    args = parser.parse_args()
    
    if args.action == 'list':
        list_available_checkpoints(args.dataset)
    
    elif args.action == 'resume':
        if not args.model_id:
            print("❌ --model_id required for resume action")
            return
        
        checkpoint_path = find_latest_checkpoint(args.dataset, args.model_id)
        if not checkpoint_path:
            print(f"❌ No checkpoint found for model: {args.model_id}")
            return
        
        resume_training(checkpoint_path, args.epochs)
    
    elif args.action == 'auto':
        if not args.dataset:
            print("❌ --dataset required for auto action")
            return
        
        checkpoint_path = find_latest_checkpoint(args.dataset)
        if not checkpoint_path:
            print(f"❌ No checkpoints found for dataset: {args.dataset}")
            return
        
        resume_training(checkpoint_path, args.epochs)

if __name__ == "__main__":
    main()