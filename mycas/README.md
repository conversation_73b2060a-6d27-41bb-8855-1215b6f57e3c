# MyCas: GNN + SNN Cascade Prediction Model

A state-of-the-art cascade prediction model combining Graph Neural Networks (GNN) and Spiking Neural Networks (SNN) to replace traditional Bi-LSTM approaches for information cascade prediction.

## 📋 Table of Contents
- [Background & Theory](#background--theory)
- [Model Architecture](#model-architecture)
- [Installation & Setup](#installation--setup)
- [Quick Start](#quick-start)
- [Training Guide](#training-guide)
- [Evaluation & Testing](#evaluation--testing)
- [Model Configurations](#model-configurations)
- [Advanced Usage](#advanced-usage)
- [Results & Analysis](#results--analysis)
- [Troubleshooting](#troubleshooting)

## 🎯 Background & Theory

### Information Cascade Prediction
Information cascade prediction aims to forecast the final size or growth pattern of information diffusion in social networks. Given the early spreading pattern of a piece of information (e.g., retweets, shares), the goal is to predict how many users will eventually be influenced.

### Why GNN + SNN?
Traditional approaches use RNNs/LSTMs for temporal modeling, but they have limitations:
- **RNN/LSTM Issues**: Vanishing gradients, sequential processing bottleneck
- **GNN Advantages**: Captures spatial relationships in cascade graphs
- **SNN Benefits**: Biologically-inspired temporal processing, energy efficiency, natural spike-based encoding

### Key Innovations
1. **Multi-scale Temporal Processing**: Captures patterns at different time horizons
2. **Enhanced Graph Construction**: 5 types of node features, 4 types of edges
3. **Cross-attention Fusion**: Combines GNN spatial and SNN temporal representations
4. **Advanced Training**: Mixed precision, gradient clipping, cosine annealing

## 🏗️ Model Architecture

### 1. Data Processing Pipeline
```
Raw Cascade Data → Enhanced Feature Engineering → Multi-scale Graph Construction → 
GNN Encoding → SNN Temporal Processing → Cross-attention Fusion → MLP Prediction
```

### 2. Core Components

#### Enhanced Graph Construction
- **Node Features (5 types)**:
  - Depth distribution features (original cascade structure)
  - Enhanced temporal features (velocity, acceleration, cyclic encoding)
  - Structural graph features (entropy, Gini coefficient, balance)
  - Cascade dynamics (growth rate, virality, cumulative growth)
  - Influence propagation (potential, shallow vs deep influence)

- **Edge Types (4 types)**:
  - Temporal edges (sequential connections)
  - Similarity edges (cosine + structural similarity)
  - Influence edges (skip connections based on influence strength)
  - Self-loops (adaptive weights)

#### Graph Neural Network (GNN) Module
- **Multi-scale Processing**: 4 different temporal scales (1x, 2x, 4x, 8x)
- **Layer Types**: GAT, GCN, GraphSAGE, GIN, Transformer-based
- **Attention Mechanisms**: Multi-head attention for different cascade aspects
- **Feature Aggregation**: Concatenation of multi-scale representations

#### Spiking Neural Network (SNN) Module
- **Neuron Types**: LIF (Leaky Integrate-and-Fire), IF, Adaptive
- **Surrogate Functions**: Sigmoid, SuperSpike, Triangle, MultiGauss
- **Temporal Integration**: Weighted, exponential, adaptive
- **Spike Encoding**: Rate-based and temporal encoding schemes

#### Fusion & Prediction
- **Cross-attention**: Between GNN and SNN features
- **Gated Fusion**: Learnable gates for feature combination
- **Residual MLP**: Skip connections for robust prediction
- **Multi-loss Training**: Combined MSE + MAE for robustness

## 🛠️ Installation & Setup

### Requirements
```bash
# Core dependencies
torch>=1.10.1
torch-geometric>=2.0.0
numpy>=1.21.0
scikit-learn>=1.0.0
pandas>=1.3.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Optional for advanced features
tensorboard>=2.8.0
wandb>=0.12.0  # For experiment tracking
```

### Installation
```bash
# Clone repository
git clone <repository-url>
cd mycas

# Create conda environment
conda create -n mycas python=3.8
conda activate mycas

# Install PyTorch with CUDA 12.0 support
conda install pytorch torchvision torchaudio pytorch-cuda=12.0 -c pytorch -c nvidia

# Install PyTorch Geometric
pip install torch-geometric

# Install other dependencies
pip install -r requirements.txt
```

### Dataset Setup
Download the CasFlow dataset (same as Cas3D reference):
- [Google Drive](https://drive.google.com/file/d/1o4KAZs19fl4Qa5LUtdnmNy57gHa15AF-/view?usp=sharing)
- [Baidu Drive (password: `1msd`)](https://pan.baidu.com/s/1tWcEefxoRHj002F0s9BCTQ)

Extract to `mycas/data/` directory (same structure as ref/cas3d):
```
mycas/data/
├── twitter/
│   └── dataset.txt
└── weibo/
    └── dataset.txt
```

**Note**: The model uses the same dataset format and splitting strategy as Cas3D for fair comparison. The single `dataset.txt` file is automatically split into train/validation/test sets (70%/15%/15%) using the same random seed for reproducible results.

## 🚀 Quick Start

### Basic Training
```bash
# Train on Twitter dataset with default settings
python mycas/run_model.py twitter 0

# Train on Weibo dataset
python mycas/run_model.py weibo 0

# Use specific GPU
python mycas/run_model.py twitter 1  # Use GPU 1
```

### Quick Test
```bash
# Quick test with reduced epochs
python mycas/test_and_measure.py twitter --epochs 20 --runs 2
```

## 📚 Training Guide

### 1. Basic Training

#### Single Model Training
```bash
# Basic training with default configuration
python mycas/run_model.py twitter 0

# Training with custom parameters (edit run_model.py)
# Modify GNN_PRESET and SNN_PRESET variables
GNN_PRESET = 'gat_gcn'      # Options: 'gat_gcn', 'gcn_only', 'gat_only', etc.
SNN_PRESET = 'lif_sigmoid'  # Options: 'lif_sigmoid', 'lif_superspike', etc.
```

#### Configuration Presets
Available GNN presets:
- `'gat_gcn'`: GAT + GCN combination (recommended)
- `'gcn_only'`: Pure GCN layers
- `'gat_only'`: Pure GAT layers  
- `'sage_gin'`: GraphSAGE + GIN
- `'transformer'`: Graph Transformer
- `'residual_gat'`: GAT with residual connections

Available SNN presets:
- `'lif_sigmoid'`: LIF neurons with sigmoid surrogate
- `'lif_superspike'`: LIF with SuperSpike surrogate
- `'if_triangle'`: IF neurons with triangle surrogate
- `'adaptive_multigauss'`: Adaptive neurons with MultiGauss

### 2. Dataset Configuration

#### Twitter Dataset Parameters
```python
# Long-term prediction (32 days)
config = Config(
    dataset='twitter',
    pred_time=3600*24*32,       # 32 days prediction
    observation_time=1*24*3600, # 1 day observation
    time_interval=900,          # 15 minutes intervals
    Maxdepth=5,                 # Maximum cascade depth
    gnn_hidden_size=128,
    snn_hidden_size=64,
    batch_size=32,
    lr=0.001
)
```

#### Weibo Dataset Parameters
```python
# Short-term prediction (1 day)
config = Config(
    dataset='weibo',
    pred_time=24*3600,          # 1 day prediction
    observation_time=1*3600,    # 1 hour observation
    time_interval=36,           # 36 seconds intervals
    Maxdepth=30,                # Maximum cascade depth
    gnn_hidden_size=128,
    snn_hidden_size=64,
    batch_size=32,
    lr=0.001
)
```

### 3. Data Processing

#### Automatic Data Splitting
The model uses the same data splitting strategy as Cas3D:
```python
# Automatic splitting (same as ref/cas3d)
Train: 70% of valid cascades
Validation: 15% of valid cascades  
Test: 15% of valid cascades

# Same filtering criteria as Cas3D:
# - Weibo: Filter by hour (8-18 or 8-19 depending on observation time)
# - Twitter: Filter by date (before April 10th)
# - Random seed: 0 (for reproducible splits)
```

#### Feature Engineering
```python
# Depth Distribution Features (compatible with Cas3D)
time_interval_num = ceil(observation_time / time_interval)
depth_features = Maxdepth - 2  # Depth starts from 2

# Enhanced features (MyCas additions)
+ Enhanced temporal features
+ Structural graph features  
+ Cascade dynamics
+ Influence propagation features
```

### 4. Training Monitoring

#### Progress Tracking
```bash
# Training outputs progress every 10 epochs
Epoch 1/100: Train=0.2341, Val=0.2156, Test=0.2089/15.23%, LR=0.001000
Epoch 11/100: Train=0.1876, Val=0.1923, Test=0.1834/12.45%, LR=0.000950
...
Early stopping at epoch 67
```

#### Key Metrics During Training
- **Train/Val/Test MSLE**: Mean Squared Logarithmic Error
- **MAPE**: Mean Absolute Percentage Error
- **LR**: Current learning rate (with scheduling)
- **Early Stopping**: Prevents overfitting (patience=10)

## 🧪 Evaluation & Testing

### 1. Comprehensive Testing Suite

#### Full Model Comparison
```bash
# Test all GNN and SNN combinations
python mycas/test_and_measure.py twitter --epochs 50 --runs 3

# Test specific configurations
python mycas/test_and_measure.py weibo --gnn_types gat gcn --epochs 30 --runs 5

# Quick evaluation
python mycas/test_and_measure.py twitter --epochs 20 --runs 2
```

#### Custom Testing Parameters
```bash
# Test with specific dataset parameters (matching Cas3D format)
python mycas/test_and_measure.py twitter \
    --pred_time 32 \           # 32 days (in days for convenience)
    --obs_time 1 \             # 1 day observation
    --time_interval 900 \      # 15 minutes
    --epochs 50 \
    --runs 3
```

### 2. Evaluation Metrics

#### Primary Metrics (Same as Cas3D)
- **MSLE** (Mean Squared Logarithmic Error): Primary metric for cascade prediction
- **MAPE** (Mean Absolute Percentage Error): Percentage-based error

#### Additional Metrics (MyCas Extensions)
- **MAE** (Mean Absolute Error): Absolute difference
- **SMAPE** (Symmetric MAPE): Symmetric percentage error
- **R²** (R-squared): Coefficient of determination
- **Pearson/Spearman Correlation**: Linear and rank correlation

#### Advanced Analysis
- **Hit Rates**: Percentage of predictions within error bounds (10%, 20%, 50%)
- **Size-based Performance**: Separate metrics for small/medium/large cascades
- **Trend Accuracy**: Ability to predict growth/decline trends

### 3. Model Validation

#### Data Splitting (Compatible with Cas3D)
```python
# Same splitting strategy as ref/cas3d for fair comparison
Train: 70% of valid cascades
Validation: 15% of valid cascades (for early stopping)
Test: 15% of valid cascades (for final evaluation)

# Same random seed (0) for reproducible results
# Same filtering criteria for temporal constraints
```

#### Statistical Analysis
```python
# Results aggregation across multiple runs
Mean ± Standard Deviation
Minimum and Maximum values
Confidence intervals
Statistical significance testing
```

## ⚙️ Model Configurations

### 1. GNN Layer Types

#### Graph Attention Network (GAT)
```python
# Best for: Attention-based node relationships
config.gnn_layer_type = 'gat'
config.num_attention_heads = 4  # Multi-head attention
```

#### Graph Convolutional Network (GCN)
```python
# Best for: Basic structural learning
config.gnn_layer_type = 'gcn'
config.gnn_hidden_size = 128
```

#### GraphSAGE
```python
# Best for: Large-scale graphs, inductive learning
config.gnn_layer_type = 'sage'
config.sage_aggregator = 'mean'  # or 'max', 'lstm'
```

#### Graph Isomorphism Network (GIN)
```python
# Best for: Distinguishing graph structures
config.gnn_layer_type = 'gin'
config.gin_epsilon = 0.1
```

### 2. SNN Neuron Types

#### Leaky Integrate-and-Fire (LIF)
```python
# Most common, good temporal dynamics
config.snn_neuron_type = 'lif'
config.snn_tau_mem = 10.0      # Membrane time constant
config.snn_tau_syn = 5.0       # Synaptic time constant
config.snn_threshold = 1.0     # Firing threshold
```

#### Integrate-and-Fire (IF)
```python
# Simpler, faster computation
config.snn_neuron_type = 'if'
config.snn_threshold = 1.0
```

#### Adaptive Neurons
```python
# Advanced, adaptive thresholds
config.snn_neuron_type = 'adaptive'
config.snn_adaptation_rate = 0.1
```

### 3. Surrogate Functions

#### Sigmoid
```python
# Smooth gradients, stable training
config.snn_surrogate_type = 'sigmoid'
config.surrogate_beta = 5.0    # Steepness parameter
```

#### SuperSpike
```python
# Biologically plausible, good performance
config.snn_surrogate_type = 'superspike'
config.surrogate_beta = 100.0
```

#### Triangle
```python
# Fast computation, good approximation
config.snn_surrogate_type = 'triangle'
```

## 🔬 Advanced Usage

### 1. Model Management

#### Save and Resume Training
```bash
# List available models
python mycas/model_resume.py list --dataset twitter

# Resume training from checkpoint
python mycas/model_resume.py resume --model_id twitter_gat_lif_20241201_143022 --epochs 30

# Compare different checkpoints
python mycas/model_resume.py compare --model_id twitter_gat_lif_20241201_143022
```

#### Model Versioning
```python
# Automatic model versioning
Model ID format: {dataset}_{gnn_type}_{snn_type}_{timestamp}
Example: twitter_gat_lif_20241201_143022

# Saved files per model:
mycas/experiments/twitter_gat_lif_20241201_143022/
├── best_model.pth          # Best performing checkpoint
├── model_epoch_10.pth      # Periodic checkpoints
├── model_epoch_20.pth
├── metadata.json           # Model configuration and metrics
└── training_log.json       # Detailed training history
```

### 2. Experiment Tracking

#### Comprehensive Logging
```python
# Automatic experiment tracking
- Training metrics per epoch
- Validation metrics per epoch  
- Test metrics per epoch
- Learning rate scheduling
- Model checkpoints
- Configuration parameters
- Training time tracking
```

#### Results Analysis
```bash
# Generated analysis files
mycas/experiments/results_twitter_20241201_143022/
├── detailed_results.json      # Complete results data
├── summary_results.csv        # Summary table
├── performance_comparison.png # Performance plots
├── metrics_distribution.png   # Metrics visualization
└── analysis_report.txt        # Comprehensive text report
```

### 3. Comparison with Cas3D

#### Fair Comparison Setup
```bash
# Use same dataset and parameters as Cas3D
# Twitter comparison
python mycas/run_model.py twitter 0  # MyCas
python ref/cas3d/run_model.py twitter 0  # Cas3D reference

# Weibo comparison  
python mycas/run_model.py weibo 0  # MyCas
python ref/cas3d/run_model.py weibo 0  # Cas3D reference
```

#### Results Comparison
```python
# Both models use:
- Same dataset files (data/{dataset}/dataset.txt)
- Same train/val/test split (70%/15%/15%)
- Same random seed (0)
- Same evaluation metrics (MSLE, MAPE)
- Same target transformation (log2(y+1))
```

## 📊 Results & Analysis

### 1. Performance Benchmarks

#### Expected Performance Ranges
```
Twitter Dataset (32-day prediction):
- Cas3D Baseline: MSLE ~0.20-0.25, MAPE ~25-30%
- MyCas Target: MSLE <0.15, MAPE <20%

Weibo Dataset (1-day prediction):
- Cas3D Baseline: MSLE ~0.15-0.20, MAPE ~20-25%
- MyCas Target: MSLE <0.12, MAPE <18%
```

#### Typical Training Times
```
Configuration complexity vs Training time:
- Simple (GCN + IF): ~10-15 minutes/epoch
- Medium (GAT + LIF): ~15-25 minutes/epoch  
- Complex (Multi-scale + Adaptive): ~25-40 minutes/epoch

Dataset size impact:
- Twitter: ~2-3x longer than Weibo
- Batch size 32: Baseline
- Batch size 16: ~1.5x longer, often better convergence
```

### 2. Model Comparison

#### Architecture Performance Ranking
```
Typical performance ranking (dataset-dependent):
1. GAT + LIF (SuperSpike): Best overall
2. GAT + GCN + LIF (Sigmoid): Good balance
3. GraphSAGE + Adaptive: Good for large cascades
4. GCN + IF: Fast, decent performance
5. Transformer + LIF: High variance, potential best
```

#### Configuration Recommendations
```
For Twitter (long-term prediction):
- GNN: GAT or GAT+GCN combination
- SNN: LIF with SuperSpike surrogate
- Hidden sizes: 128-256
- Attention heads: 4-8

For Weibo (short-term prediction):
- GNN: GCN or GraphSAGE
- SNN: LIF with Sigmoid surrogate  
- Hidden sizes: 64-128
- Attention heads: 2-4
```

### 3. Analysis Tools

#### Automatic Report Generation
```bash
# Generates comprehensive analysis
python mycas/test_and_measure.py twitter --epochs 50 --runs 3

# Output includes:
- Performance comparison plots
- Statistical significance tests
- Best configuration identification
- Detailed metrics breakdown
- Training time analysis
- Comparison with Cas3D baseline
```

#### Custom Analysis
```python
# Load results for custom analysis
import json
with open('mycas/experiments/results_twitter_20241201/detailed_results.json', 'r') as f:
    results = json.load(f)

# Extract specific metrics
for config, metrics in results.items():
    print(f"{config}: MSLE={metrics['avg_test_msle']:.4f}")
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Dataset Loading Issues
```bash
# Ensure correct dataset structure
mycas/data/twitter/dataset.txt  # Single file, same as Cas3D
mycas/data/weibo/dataset.txt    # Single file, same as Cas3D

# Check file format (tab-separated)
cascade_id \t user_id \t timestamp \t num_adoptions \t adoption_paths
```

#### 2. CUDA Out of Memory
```bash
# Solutions:
- Reduce batch_size: 32 → 16 → 8
- Reduce hidden_size: 256 → 128 → 64
- Use gradient checkpointing (automatic in model)
- Use smaller graphs (reduce time_interval_num)
```

#### 3. Training Instability
```bash
# Solutions:
- Lower learning rate: 0.001 → 0.0005
- Increase gradient clipping: max_norm=1.0 → 0.5
- Add more regularization: dropout=0.1 → 0.3
- Use mixed precision training (automatic)
```

#### 4. Poor Convergence
```bash
# Solutions:
- Increase model capacity: hidden_size, num_layers
- Try different GNN/SNN combinations
- Adjust learning rate schedule
- Increase training epochs
- Check data preprocessing
```

### Performance Optimization

#### 1. Speed Optimization
```python
# Fast training settings
config.batch_size = 64        # Larger batches
config.num_workers = 4        # More data loading workers
config.pin_memory = True      # Faster GPU transfer
config.epochs = 50            # Fewer epochs for quick results
```

#### 2. Memory Optimization
```python
# Memory-efficient settings
config.batch_size = 8         # Smaller batches
config.gnn_hidden_size = 64   # Smaller hidden dimensions
config.gradient_checkpointing = True  # Trade compute for memory
```

#### 3. Accuracy Optimization
```python
# High-accuracy settings
config.epochs = 200           # More training
config.lr = 0.0001           # Lower learning rate
config.batch_size = 16        # Smaller batches for stability
config.num_attention_heads = 8 # More attention
```

## 📈 Usage Examples

### Example 1: Quick Evaluation
```bash
# Test best configurations quickly
python mycas/test_and_measure.py twitter --epochs 20 --runs 2 --gnn_types gat gcn
```

### Example 2: Comprehensive Benchmark
```bash
# Full benchmark with statistical significance
python mycas/test_and_measure.py twitter --epochs 100 --runs 5
```

### Example 3: Compare with Cas3D
```bash
# Train MyCas
python mycas/run_model.py twitter 0

# Train Cas3D reference (for comparison)
python ref/cas3d/run_model.py twitter 0

# Results will be comparable due to same dataset and evaluation
```

### Example 4: Resume Interrupted Training
```bash
# List available models
python mycas/model_resume.py list

# Resume specific model
python mycas/model_resume.py resume --model_id twitter_gat_lif_20241201_143022 --epochs 50
```

## 📚 References & Citation

```bibtex
@article{mycas2024,
  title={MyCas: A Hybrid GNN-SNN Architecture for Information Cascade Prediction},
  author={Your Name},
  journal={Conference/Journal Name},
  year={2024}
}
```

## 📞 Support

For issues and questions:
1. Check this README for common solutions
2. Review the troubleshooting section
3. Check existing issues in the repository
4. Create a new issue with detailed error information

---

**Happy cascade predicting! 🚀**

## 🔄 Training Management & Resume

### Early Stopping
The model automatically implements early stopping to prevent overfitting:
- **Patience**: 10 epochs (stops if validation loss doesn't improve)
- **Best Model Saving**: Automatically saves the best performing model
- **Checkpoint System**: Saves model state every epoch for recovery

### Resume Interrupted Training
If training is interrupted (Ctrl+C, system crash, etc.), you can easily resume:

#### Quick Resume
```bash
# Auto-resume latest training for a dataset
python mycas/resume_training.py auto --dataset twitter --epochs 50

# List all available checkpoints
python mycas/resume_training.py list

# Resume specific model
python mycas/resume_training.py resume --model_id twitter_gat_lif_20241201_143022 --epochs 30
```

#### Manual Resume in Code
```python
# Resume from specific checkpoint
config = Config(dataset='twitter', ...)
model = MyCas(config)

# Train with resume
checkpoint_path = "mycas/checkpoints/twitter/twitter_gat_lif_20241201_143022_latest.pth"
train_msle, val_msle, test_msle, test_mape = model.train_model(X, Y, resume_from=checkpoint_path)
```

### Checkpoint Management
```bash
# Checkpoint structure
mycas/checkpoints/twitter/
├── twitter_gat_lif_20241201_143022_latest.pth    # Latest checkpoint
├── twitter_gat_lif_20241201_143022_best.pth      # Best model
└── twitter_gcn_if_20241201_150000_latest.pth     # Other models

# Each checkpoint contains:
- Model weights and optimizer state
- Training epoch and metrics
- Full configuration for exact resume
- Scheduler state for learning rate
```

### Training Interruption Handling
```bash
# If training is interrupted:
⚠️ Training interrupted at epoch 45
💾 Checkpoint saved. Resume with:
   python mycas/resume_training.py auto --dataset twitter

# The system automatically:
1. Saves current model state
2. Preserves optimizer and scheduler state  
3. Records exact epoch and metrics
4. Provides resume command
```

### Advanced Resume Options
```bash
# Resume with different parameters
python mycas/resume_training.py resume \
    --model_id twitter_gat_lif_20241201_143022 \
    --epochs 100  # Train for 100 more epochs

# List checkpoints for specific dataset
python mycas/resume_training.py list --dataset weibo

# Find and resume latest model automatically
python mycas/resume_training.py auto --dataset twitter
```


