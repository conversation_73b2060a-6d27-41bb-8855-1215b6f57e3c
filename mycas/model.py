import torch
import torch.nn as nn
import torch.nn.functional as F
from gnn_layers import TemporalGNNEncoder
from snn_layers import SpikingEncoder
from graph_builder import build_cascade_graphs
from loss import Loss
import time
from datetime import datetime, timedelta
import os
import json
from datetime import datetime

class TrainingProgressTracker:
    def __init__(self, total_epochs, total_batches_per_epoch):
        self.total_epochs = total_epochs
        self.total_batches_per_epoch = total_batches_per_epoch
        self.start_time = time.time()
        self.epoch_start_time = time.time()
        self.last_batch_time = time.time()
        self.batch_times = []
        
    def update_batch(self, epoch, batch_idx, batch_loss, lr):
        current_time = time.time()
        
        # Calculate batch time more accurately
        if batch_idx == 0:
            batch_time = current_time - self.epoch_start_time
        else:
            batch_time = current_time - self.last_batch_time
        
        self.last_batch_time = current_time
        
        # Only add reasonable batch times (between 0.1s and 300s)
        if 0.1 <= batch_time <= 300:
            self.batch_times.append(batch_time)
        
        # Keep only last 20 batch times for stable ETA
        if len(self.batch_times) > 20:
            self.batch_times.pop(0)
        
        # Calculate progress
        epoch_progress = (batch_idx + 1) / self.total_batches_per_epoch
        total_progress = (epoch + epoch_progress) / self.total_epochs
        
        # Calculate ETA with safety checks
        eta_str = "calculating..."
        if len(self.batch_times) >= 3:  # Need at least 3 samples
            avg_batch_time = sum(self.batch_times) / len(self.batch_times)
            remaining_batches = (self.total_epochs - epoch - 1) * self.total_batches_per_epoch + (self.total_batches_per_epoch - batch_idx - 1)
            eta_seconds = remaining_batches * avg_batch_time
            
            # Cap ETA at reasonable maximum (24 hours)
            if eta_seconds > 86400:  # 24 hours
                eta_str = ">24h"
            elif eta_seconds > 0:
                try:
                    eta_str = str(timedelta(seconds=int(eta_seconds)))
                except (ValueError, OverflowError):
                    eta_str = ">24h"
        
        # Format progress bar
        bar_length = 25
        filled_length = int(bar_length * epoch_progress)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        
        # Display progress with better formatting
        print(f'\rEpoch {epoch+1:3d}/{self.total_epochs} [{bar}] '
              f'{batch_idx+1:4d}/{self.total_batches_per_epoch} '
              f'({epoch_progress:5.1%}) | '
              f'Loss: {batch_loss:.4f} | '
              f'LR: {lr:.1e} | '
              f'ETA: {eta_str:<10}', end='', flush=True)
    
    def start_epoch(self, epoch):
        self.epoch_start_time = time.time()
        self.last_batch_time = time.time()
        if epoch > 0:
            print()  # New line for new epoch
    
    def finish_epoch(self, epoch, train_loss, val_loss, test_loss, test_mape):
        epoch_time = time.time() - self.epoch_start_time
        total_time = time.time() - self.start_time
        
        # Format times safely
        epoch_time_str = f"{epoch_time:.1f}s"
        if total_time > 3600:
            total_time_str = f"{total_time/3600:.1f}h"
        elif total_time > 60:
            total_time_str = f"{total_time/60:.1f}m"
        else:
            total_time_str = f"{total_time:.1f}s"
        
        print(f'\n📊 Epoch {epoch+1:3d} | '
              f'Time: {epoch_time_str} | '
              f'Train: {train_loss:.4f} | '
              f'Val: {val_loss:.4f} | '
              f'Test: {test_loss:.4f}/{test_mape:.1f}% | '
              f'Total: {total_time_str}')

class MyCas(nn.Module):
    def __init__(self, config):
        super(MyCas, self).__init__()
        self.config = config
        
        # Add checkpoint directory
        self.checkpoint_dir = f"mycas/checkpoints/{config.dataset}"
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # Generate unique model ID
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.model_id = f"{config.dataset}_{config.gnn_layer_type}_{config.snn_neuron_type}_{timestamp}"
        
        # Multi-scale temporal encoding
        self.temporal_scales = [1, 2, 4, 8]
        self.scale_encoders = nn.ModuleList([
            TemporalGNNEncoder(config) for _ in self.temporal_scales
        ])
        
        # Enhanced SNN encoder with memory
        self.snn_encoder = SpikingEncoder(config)
        
        # Cross-attention between GNN and SNN features
        # Use SNN hidden size instead of GNN hidden size for SNN features
        self.cross_attention = nn.MultiheadAttention(
            config.gnn_hidden_size, config.num_attention_heads, 
            dropout=config.dropout, batch_first=True
        )
        
        # Add projection layer to match SNN features to GNN feature size
        self.snn_projection = nn.Linear(config.snn_hidden_size, config.gnn_hidden_size)
        
        # Feature fusion with gating mechanism
        # Update fusion_dim to account for projected SNN features
        fusion_dim = config.gnn_hidden_size * 2  # GNN + projected SNN (both gnn_hidden_size)
        self.fusion_gate = nn.Sequential(
            nn.Linear(fusion_dim, fusion_dim // 2),
            nn.Sigmoid()
        )
        self.fusion_layer = nn.Sequential(
            nn.Linear(fusion_dim, config.gnn_hidden_size),
            nn.LayerNorm(config.gnn_hidden_size),
            nn.ReLU(),
            nn.Dropout(config.dropout)
        )
        
        # Residual MLP with skip connections
        mlp_layers = []
        mlp_dims = [config.gnn_hidden_size] + config.mlp
        
        for i in range(1, len(mlp_dims)):
            if i < len(mlp_dims) - 1:
                mlp_layers.extend([
                    nn.Linear(mlp_dims[i-1], mlp_dims[i]),
                    nn.LayerNorm(mlp_dims[i]),
                    nn.ReLU(inplace=True),
                    nn.Dropout(config.dropout)
                ])
            else:
                mlp_layers.append(nn.Linear(mlp_dims[i-1], mlp_dims[i]))
        
        self.mlp = nn.Sequential(*mlp_layers)
        
        # Skip connection for final prediction
        self.skip_connection = nn.Linear(config.gnn_hidden_size, config.mlp[-1])
        
        # Advanced optimizer with better convergence
        self.optimizer = torch.optim.AdamW(
            self.parameters(), 
            lr=config.lr, 
            weight_decay=1e-4,
            betas=(0.9, 0.95),  # Better beta2 for faster convergence
            eps=1e-6,           # Smaller eps for better numerical stability
            amsgrad=True        # More stable updates
        )
        
        # Use original scheduler - OneCycleLR was causing gradient explosion
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config.epochs,
            eta_min=config.lr * 0.01
        )
        
        # Move to CUDA if available during initialization
        if torch.cuda.is_available():
            self.cuda()
            
        # Better weight initialization
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize weights properly to prevent gradient explosion"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.01)  # Much smaller gain
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.MultiheadAttention):
                for param in module.parameters():
                    if param.dim() > 1:
                        nn.init.xavier_uniform_(param, gain=0.01)  # Much smaller gain
            elif isinstance(module, (nn.LayerNorm, nn.BatchNorm1d)):
                if hasattr(module, 'weight') and module.weight is not None:
                    nn.init.constant_(module.weight, 1)
                if hasattr(module, 'bias') and module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def _ensure_device_consistency(self, target_device):
        """Ensure all model components are on the target device"""
        self.to(target_device)
        
        # Explicitly move all encoders
        for i, encoder in enumerate(self.scale_encoders):
            self.scale_encoders[i] = encoder.to(target_device)
            
        # Move other components
        if hasattr(self, 'snn_encoder'):
            self.snn_encoder = self.snn_encoder.to(target_device)
        if hasattr(self, 'cross_attention'):
            self.cross_attention = self.cross_attention.to(target_device)
        if hasattr(self, 'fusion_gate'):
            self.fusion_gate = self.fusion_gate.to(target_device)
    
    def forward(self, x):
        # Build graphs and ensure all components are on the same device as input
        graph_batch = build_cascade_graphs(x, self.config)
        
        # Force all graph components to the same device as input
        target_device = x.device
        graph_batch.x = graph_batch.x.to(target_device)
        graph_batch.edge_index = graph_batch.edge_index.to(target_device)
        if hasattr(graph_batch, 'batch'):
            graph_batch.batch = graph_batch.batch.to(target_device)
        
        # Multi-scale temporal processing
        gnn_features_list = []
        for scale, i in zip(self.temporal_scales, range(len(self.scale_encoders))):
            # Ensure encoder is on correct device
            encoder = self.scale_encoders[i]
            encoder_device = next(encoder.parameters()).device
            
            if encoder_device != graph_batch.x.device:
                self.scale_encoders[i] = encoder.to(graph_batch.x.device)
                encoder = self.scale_encoders[i]
            
            scaled_features, _ = encoder(graph_batch.x, graph_batch.edge_index, graph_batch.batch)
            gnn_features_list.append(scaled_features)
        
        # Concatenate multi-scale features
        gnn_features = torch.cat(gnn_features_list, dim=1)

        # Reduce concatenated features to expected size
        if gnn_features.size(1) != self.config.gnn_hidden_size:
            if not hasattr(self, 'feature_projection'):
                self.feature_projection = nn.Linear(gnn_features.size(1), self.config.gnn_hidden_size)
            
            # Ensure projection layer is on correct device
            self.feature_projection = self.feature_projection.to(gnn_features.device)
            gnn_features = self.feature_projection(gnn_features)
        
        # SNN encoding for temporal dynamics
        # Ensure SNN encoder is on correct device
        self.snn_encoder = self.snn_encoder.to(gnn_features_list[0].device)
        snn_features = self.snn_encoder(gnn_features_list[0])

        # Project SNN features to match GNN feature size
        if not hasattr(self, 'snn_projection'):
            self.snn_projection = nn.Linear(snn_features.size(1), self.config.gnn_hidden_size)

        self.snn_projection = self.snn_projection.to(snn_features.device)
        snn_features_projected = self.snn_projection(snn_features)

        # Cross-attention between GNN and SNN
        gnn_feat_expanded = gnn_features.unsqueeze(1)
        snn_feat_expanded = snn_features_projected.unsqueeze(1)

        # Ensure cross-attention is on correct device
        self.cross_attention = self.cross_attention.to(gnn_feat_expanded.device)

        attended_gnn, _ = self.cross_attention(
            gnn_feat_expanded, snn_feat_expanded, snn_feat_expanded
        )
        attended_gnn = attended_gnn.squeeze(1)

        # Gated feature fusion - use projected SNN features
        fused_features = torch.cat([attended_gnn, snn_features_projected], dim=1)

        # Ensure fusion components are on correct device and handle dynamic sizing
        if not hasattr(self, 'fusion_gate') or self.fusion_gate[0].in_features != fused_features.size(1):
            # Recreate fusion layers with correct dimensions
            fusion_dim = fused_features.size(1)
            self.fusion_gate = nn.Sequential(
                nn.Linear(fusion_dim, fusion_dim),
                nn.Sigmoid()
            ).to(fused_features.device)
            
            self.fusion_layer = nn.Sequential(
                nn.Linear(fusion_dim, self.config.gnn_hidden_size),
                nn.LayerNorm(self.config.gnn_hidden_size),
                nn.ReLU(),
                nn.Dropout(self.config.dropout)
            ).to(fused_features.device)
        elif self.fusion_gate[0].out_features != fused_features.size(1):
            # Also check output size mismatch
            fusion_dim = fused_features.size(1)
            self.fusion_gate = nn.Sequential(
                nn.Linear(fusion_dim, fusion_dim),
                nn.Sigmoid()
            ).to(fused_features.device)
            
            self.fusion_layer = nn.Sequential(
                nn.Linear(fusion_dim, self.config.gnn_hidden_size),
                nn.LayerNorm(self.config.gnn_hidden_size),
                nn.ReLU(),
                nn.Dropout(self.config.dropout)
            ).to(fused_features.device)
        else:
            # Move existing layers to correct device
            self.fusion_gate = self.fusion_gate.to(fused_features.device)
            self.fusion_layer = self.fusion_layer.to(fused_features.device)

        gate = self.fusion_gate(fused_features)
        fused_features = fused_features * gate
        fused_features = self.fusion_layer(fused_features)

        # Residual MLP prediction
        self.mlp = self.mlp.to(fused_features.device)
        self.skip_connection = self.skip_connection.to(fused_features.device)

        mlp_output = self.mlp(fused_features)
        skip_output = self.skip_connection(fused_features)

        # Final prediction with residual connection
        output = mlp_output + skip_output

        return output
    
    def save_checkpoint(self, epoch, train_loss, val_loss, test_loss, test_mape, is_best=False):
        """Save training checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'train_loss': train_loss,
            'val_loss': val_loss,
            'test_loss': test_loss,
            'test_mape': test_mape,
            'config': self.config.__dict__,
            'model_id': self.model_id
        }
        
        # Save latest checkpoint
        latest_path = os.path.join(self.checkpoint_dir, f"{self.model_id}_latest.pth")
        torch.save(checkpoint, latest_path)
        
        # Save best model
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, f"{self.model_id}_best.pth")
            torch.save(checkpoint, best_path)
            print(f"💾 Best model saved: {best_path}")
        
        return latest_path
    
    def load_checkpoint(self, checkpoint_path):
        """Load training checkpoint"""
        if not os.path.exists(checkpoint_path):
            print(f"❌ Checkpoint not found: {checkpoint_path}")
            return None
        
        checkpoint = torch.load(checkpoint_path)
        self.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        print(f"✅ Resumed from epoch {checkpoint['epoch']}")
        print(f"   Last metrics: Val={checkpoint['val_loss']:.4f}, Test={checkpoint['test_loss']:.4f}")
        
        return checkpoint
    
    def train_model(self, X, Y, resume_from=None):
        """Enhanced training with comprehensive progress tracking"""
        from sklearn.model_selection import train_test_split
        from torch.utils.data import DataLoader, TensorDataset
        import numpy as np
        
        # X and Y are already lists of tensors from data loading
        X_train, X_val, X_test = X[0], X[1], X[2]
        Y_train, Y_val, Y_test = Y[0], Y[1], Y[2]
        
        # Convert to CPU tensors first (remove .cuda() calls)
        if not isinstance(X_train, torch.Tensor):
            X_train = torch.FloatTensor(X_train)
            Y_train = torch.FloatTensor(Y_train)
            X_val = torch.FloatTensor(X_val)
            Y_val = torch.FloatTensor(Y_val)
            X_test = torch.FloatTensor(X_test)
            Y_test = torch.FloatTensor(Y_test)
        else:
            # Move to CPU if already on CUDA
            X_train = X_train.cpu()
            Y_train = Y_train.cpu()
            X_val = X_val.cpu()
            Y_val = Y_val.cpu()
            X_test = X_test.cpu()
            Y_test = Y_test.cpu()
        
        # Create datasets with CPU tensors
        train_dataset = TensorDataset(X_train, Y_train)
        val_dataset = TensorDataset(X_val, Y_val)
        test_dataset = TensorDataset(X_test, Y_test)
        
        # DataLoaders with pin_memory for faster GPU transfer
        dataloader_train = DataLoader(
            train_dataset, batch_size=self.config.batch_size, 
            shuffle=True, num_workers=0, pin_memory=True
        )
        dataloader_val = DataLoader(
            val_dataset, batch_size=self.config.batch_size, 
            shuffle=False, num_workers=0, pin_memory=True
        )
        dataloader_test = DataLoader(
            test_dataset, batch_size=self.config.batch_size, 
            shuffle=False, num_workers=0, pin_memory=True
        )
        
        # Enhanced loss function
        loss_mse = nn.MSELoss()
        loss_mae = nn.L1Loss()
        
        # Training tracking
        best_val_loss = float('inf')
        patience_counter = 0
        patience = 10  # Stop after 10 epochs without improvement
        start_epoch = 0
        
        # Resume training if checkpoint provided
        if resume_from:
            checkpoint = self.load_checkpoint(resume_from)
            if checkpoint:
                start_epoch = checkpoint['epoch'] + 1
                best_val_loss = checkpoint['val_loss']
                print(f"🔄 Resuming training from epoch {start_epoch}")
        
        best_result = [float('inf')] * 6
        
        # Move model to CUDA if available
        if torch.cuda.is_available():
            device = torch.device('cuda:0')
            self._ensure_device_consistency(device)
        
        # Setup optimizer - back to original simple setup
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.config.lr * 0.1,  # Much lower learning rate
            weight_decay=self.config.weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8,
            amsgrad=False
        )
        
        # Initialize progress tracker
        progress_tracker = TrainingProgressTracker(
            total_epochs=self.config.epochs,
            total_batches_per_epoch=len(dataloader_train)
        )
        
        print(f"🚀 Starting training: {self.config.epochs} epochs, {len(dataloader_train)} batches/epoch")
        print(f"📊 Dataset: Train={len(X_train)}, Val={len(X_val)}, Test={len(X_test)}")
        print(f"⚙️  Config: {self.config.toString()}")
        print("=" * 80)
        
        try:
            for epoch in range(start_epoch, self.config.epochs):
                progress_tracker.start_epoch(epoch)
                
                # Training phase
                self.train()
                train_losses = []
                
                for batch_idx, (batch_x, batch_y) in enumerate(dataloader_train):
                    try:
                        if torch.cuda.is_available():
                            batch_x = batch_x.cuda()
                            batch_y = batch_y.cuda()
                        
                        optimizer.zero_grad()
                        outputs = self(batch_x)
                        loss = loss_mse(outputs, batch_y)
                        loss.backward()
                        
                        # Gradient clipping with adaptive norm - much stricter
                        grad_norm = torch.nn.utils.clip_grad_norm_(self.parameters(), max_norm=0.1)
                        
                        # Skip update if gradients are too large (numerical instability)
                        if grad_norm < 5.0:  # Much stricter threshold
                            optimizer.step()
                            train_losses.append(loss.item())
                        else:
                            print(f"\nSkipping batch {batch_idx} due to large gradients: {grad_norm:.2f}")
                            continue
                        
                        # Update scheduler every epoch, not every batch
                        # self.scheduler.step() - move this to after validation
                        
                        # Update progress display
                        current_lr = optimizer.param_groups[0]['lr']
                        progress_tracker.update_batch(epoch, batch_idx, loss.item(), current_lr)
                    
                    except Exception as e:
                        print(f"\n❌ Training error in batch {batch_idx}: {e}")
                        continue
                
                # Validation phase
                self.eval()
                val_losses = []
                
                with torch.no_grad():
                    for batch_x, batch_y in dataloader_val:
                        if torch.cuda.is_available():
                            batch_x, batch_y = batch_x.cuda(), batch_y.cuda()
                        
                        try:
                            predictions = self(batch_x)
                            loss = loss_mse(predictions, batch_y)
                            val_losses.append(loss.item())
                        except Exception as e:
                            print(f"Validation error: {e}")
                            continue
                
                # Calculate metrics
                train_msle = np.mean(train_losses) if train_losses else float('inf')
                val_msle = np.mean(val_losses) if val_losses else float('inf')
                
                # Test evaluation
                test_msle, test_mape = self._evaluate_test(dataloader_test, loss_mse)
                
                # Learning rate scheduling - move back to after validation
                self.scheduler.step()
                
                # Early stopping check
                is_best = val_msle < best_val_loss
                if is_best:
                    best_val_loss = val_msle
                    patience_counter = 0
                    best_result = [train_msle, 0, val_msle, 0, test_msle, test_mape]
                else:
                    patience_counter += 1
                
                # Save checkpoint every epoch
                self.save_checkpoint(epoch, train_msle, val_msle, test_msle, test_mape, is_best)
                
                # Early stopping
                if patience_counter >= patience:
                    print(f"\n⏹️ Early stopping at epoch {epoch+1} (patience={patience})")
                    print(f"   No improvement for {patience} epochs")
                    break
                
                # Progress update
                if epoch % 10 == 0 or is_best:
                    progress_tracker.finish_epoch(epoch, train_msle, val_msle, test_msle, test_mape)
        
        except KeyboardInterrupt:
            print(f"\n⚠️ Training interrupted at epoch {epoch}")
            print(f"💾 Checkpoint saved. Resume with:")
            print(f"   python mycas/resume_training.py {self.model_id}")
            return best_result[0], best_result[2], best_result[4], best_result[5]
        
        # Load best model for final evaluation
        best_path = os.path.join(self.checkpoint_dir, f"{self.model_id}_best.pth")
        if os.path.exists(best_path):
            best_checkpoint = torch.load(best_path)
            self.load_state_dict(best_checkpoint['model_state_dict'])
            print(f"✅ Loaded best model from epoch {best_checkpoint['epoch']}")
        
        return best_result[0], best_result[2], best_result[4], best_result[5]
    
    def _evaluate_test(self, dataloader_test, loss_fn):
        """Enhanced test evaluation"""
        self.eval()
        test_total_loss = 0
        test_batch_count = 0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_x, batch_y in dataloader_test:
                if torch.cuda.is_available():
                    batch_x, batch_y = batch_x.cuda(), batch_y.cuda()
                
                try:
                    predictions = self(batch_x)
                    loss = loss_fn(predictions, batch_y)
                    test_total_loss += loss.item()
                    test_batch_count += 1
                    
                    all_predictions.append(predictions.cpu())
                    all_targets.append(batch_y.cpu())
                except:
                    continue
        
        if test_batch_count == 0:
            return float('inf'), float('inf')
        
        # Calculate comprehensive metrics
        test_msle = test_total_loss / test_batch_count
        
        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)
        
        # MAPE calculation with numerical stability
        epsilon = 1e-8
        test_mape = torch.mean(torch.abs((all_targets - all_predictions) / 
                                       (all_targets + epsilon))).item() * 100
        
        return test_msle, test_mape
