"""
Easy script for comparing different GNN and SNN layer combinations
"""
import os
import sys
import torch
from itertools import product

sys.path.append(os.path.dirname(__file__))

from config import Config, get_gnn_config_presets, get_snn_config_presets
from model import MyCas
from data_processor import load_cascade_data, preprocess_data

def quick_test_layers(dataset='twitter', max_epochs=20):
    """Quick test of different layer combinations"""
    
    print(f"=== Quick Layer Comparison for {dataset} ===")
    
    # Basic setup
    if dataset == 'weibo':
        pred_time, obs_time, time_interval = 24*3600, 1*3600, 36
    else:
        pred_time, obs_time, time_interval = 3600*24*32, 1*24*3600, 900
    
    # ============ CONFIGURE LAYERS TO TEST HERE ============
    
    # GNN layers to test
    gnn_layers_to_test = [
        'gcn',           # Graph Convolutional Network
        'gat',           # Graph Attention Network  
        'sage',          # GraphSAGE
        'gin',           # Graph Isomorphism Network
        'transformer',   # Graph Transformer
    ]
    
    # SNN configurations to test
    snn_configs_to_test = [
        {'neuron': 'lif', 'surrogate': 'sigmoid'},
        {'neuron': 'lif', 'surrogate': 'superspike'},
        {'neuron': 'if', 'surrogate': 'triangle'},
        {'neuron': 'adaptive', 'surrogate': 'multigauss'},
    ]
    
    # ======================================================
    
    results = []
    
    # Load data once
    base_config = Config(dataset, pred_time, obs_time, time_interval, epochs=max_epochs)
    X, Y = load_cascade_data(base_config)
    X, Y = preprocess_data(X, Y, base_config)
    
    print(f"\nTesting {len(gnn_layers_to_test)} GNN types × {len(snn_configs_to_test)} SNN configs")
    print("=" * 60)
    
    for gnn_type in gnn_layers_to_test:
        for snn_config in snn_configs_to_test:
            
            config_name = f"{gnn_type}+{snn_config['neuron']}_{snn_config['surrogate']}"
            print(f"\nTesting: {config_name}")
            
            try:
                # Create config
                config = Config(
                    dataset, pred_time, obs_time, time_interval,
                    gnn_first_layer_type=gnn_type,
                    gnn_layer_type=gnn_type,
                    snn_neuron_type=snn_config['neuron'],
                    snn_surrogate_type=snn_config['surrogate'],
                    epochs=max_epochs,
                    batch_size=16  # Smaller batch for quick testing
                )
                
                # Train model
                model = MyCas(config)
                _, _, test_msle, test_mape = model.train_model(X, Y)
                
                result = {
                    'name': config_name,
                    'gnn_type': gnn_type,
                    'snn_neuron': snn_config['neuron'],
                    'snn_surrogate': snn_config['surrogate'],
                    'test_msle': test_msle,
                    'test_mape': test_mape
                }
                results.append(result)
                
                print(f"✓ {config_name}: MSLE={test_msle:.4f}, MAPE={test_mape:.4f}")
                
            except Exception as e:
                print(f"✗ {config_name}: Failed - {e}")
                continue
    
    # Print summary
    print("\n" + "=" * 60)
    print("SUMMARY RESULTS (sorted by MSLE):")
    print("=" * 60)
    
    if results:
        # Sort by test_msle
        results.sort(key=lambda x: x['test_msle'])
        
        print(f"{'Rank':<4} {'Configuration':<25} {'MSLE':<8} {'MAPE':<8}")
        print("-" * 50)
        
        for i, result in enumerate(results, 1):
            print(f"{i:<4} {result['name']:<25} {result['test_msle']:<8.4f} {result['test_mape']:<8.4f}")
        
        # Save results
        with open(f"mycas/{dataset}_layer_comparison.txt", 'w') as f:
            f.write("Layer Comparison Results\n")
            f.write("=" * 30 + "\n")
            for i, result in enumerate(results, 1):
                f.write(f"{i}. {result['name']}: MSLE={result['test_msle']:.4f}, MAPE={result['test_mape']:.4f}\n")
        
        print(f"\nBest configuration: {results[0]['name']}")
        print(f"Performance: MSLE={results[0]['test_msle']:.4f}, MAPE={results[0]['test_mape']:.4f}")
        
    else:
        print("No successful results!")

def detailed_comparison(dataset='twitter'):
    """Detailed comparison with all preset combinations"""
    
    print(f"=== Detailed Comparison for {dataset} ===")
    
    gnn_presets = get_gnn_config_presets()
    snn_presets = get_snn_config_presets()
    
    print(f"Testing {len(gnn_presets)} GNN presets × {len(snn_presets)} SNN presets")
    
    # Basic setup
    if dataset == 'weibo':
        pred_time, obs_time, time_interval = 24*3600, 1*3600, 36
    else:
        pred_time, obs_time, time_interval = 3600*24*32, 1*24*3600, 900
    
    results = []
    
    for gnn_name, gnn_config in gnn_presets.items():
        for snn_name, snn_config in snn_presets.items():
            
            config_name = f"{gnn_name}+{snn_name}"
            print(f"\nTesting: {config_name}")
            
            try:
                # Merge configurations
                merged_config = {**gnn_config, **snn_config}
                
                config = Config(
                    dataset, pred_time, obs_time, time_interval,
                    epochs=30, batch_size=16, **merged_config
                )
                
                # Load data and train
                X, Y = load_cascade_data(config)
                X, Y = preprocess_data(X, Y, config)
                
                model = MyCas(config)
                _, _, test_msle, test_mape = model.train_model(X, Y)
                
                results.append({
                    'name': config_name,
                    'test_msle': test_msle,
                    'test_mape': test_mape
                })
                
                print(f"✓ {config_name}: MSLE={test_msle:.4f}, MAPE={test_mape:.4f}")
                
            except Exception as e:
                print(f"✗ {config_name}: Failed - {e}")
                continue
    
    # Print and save results
    if results:
        results.sort(key=lambda x: x['test_msle'])
        
        print("\n" + "=" * 50)
        print("DETAILED COMPARISON RESULTS:")
        print("=" * 50)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['name']}: MSLE={result['test_msle']:.4f}, MAPE={result['test_mape']:.4f}")
        
        # Save detailed results
        with open(f"mycas/{dataset}_detailed_comparison.txt", 'w') as f:
            f.write("Detailed Layer Comparison Results\n")
            f.write("=" * 40 + "\n")
            for i, result in enumerate(results, 1):
                f.write(f"{i}. {result['name']}: MSLE={result['test_msle']:.4f}, MAPE={result['test_mape']:.4f}\n")

if __name__ == '__main__':
    dataset = sys.argv[1] if len(sys.argv) > 1 else 'twitter'
    mode = sys.argv[2] if len(sys.argv) > 2 else 'quick'
    
    if mode == 'quick':
        quick_test_layers(dataset)
    elif mode == 'detailed':
        detailed_comparison(dataset)
    else:
        print("Usage: python layer_comparison.py [dataset] [quick|detailed]")