import math

class Config():
    def __init__(self, dataset, pred_time, observation_time, time_interval, 
                 gnn_hidden_size=128, snn_hidden_size=64, num_gnn_layers=2, 
                 num_snn_layers=2, num_attention_heads=4, mlp=[128, 64, 1], 
                 lr=0.001, batch_size=32, epochs=1000,
                 # GNN Configuration Options
                 gnn_first_layer_type='gat', gnn_layer_type='gcn', 
                 graph_pooling_type='mean_max',
                 # SNN Configuration Options  
                 snn_neuron_type='lif', snn_surrogate_type='sigmoid',
                 snn_temporal_integration='weighted', snn_noise_scale=0.1):
        
        # Basic settings (compatible with Cas3D)
        self.dataset = dataset
        self.observation_time = observation_time
        self.time_interval = time_interval
        self.pred_time = pred_time
        self.mlp = mlp
        self.batch_size = batch_size
        self.lr = lr
        self.epochs = epochs
        self.weight_decay = 5e-4  # Slightly higher weight decay
        self.dropout = 0.1
        
        # Dataset-specific settings
        if dataset == 'twitter':
            self.Maxdepth = 5
        elif dataset == 'weibo':
            self.Maxdepth = 30
        else:
            self.Maxdepth = 10  # default
            
        self.time_interval_num = math.ceil(observation_time / time_interval)
        
        # GNN settings
        self.gnn_hidden_size = gnn_hidden_size
        self.num_gnn_layers = num_gnn_layers
        self.num_attention_heads = num_attention_heads
        self.dropout = 0.1
        
        # ============ GNN CONFIGURATION OPTIONS ============
        # Available GNN layer types:
        # 'gcn', 'gat', 'sage', 'gin', 'transformer', 'residual_gcn', 'residual_gat'
        self.gnn_first_layer_type = gnn_first_layer_type  # First layer type
        self.gnn_layer_type = gnn_layer_type              # Subsequent layers type
        
        # Available pooling types:
        # 'mean', 'max', 'add', 'mean_max', 'mean_max_add'
        self.graph_pooling_type = graph_pooling_type
        
        # SNN settings
        self.snn_hidden_size = snn_hidden_size
        self.num_snn_layers = num_snn_layers
        self.spike_threshold = 1.0
        self.membrane_decay = 0.9
        self.spike_timesteps = 10
        
        # ============ SNN CONFIGURATION OPTIONS ============
        # Available neuron types:
        # 'lif' (Leaky Integrate-and-Fire)
        # 'if' (Integrate-and-Fire) 
        # 'adaptive' (Adaptive threshold)
        self.snn_neuron_type = snn_neuron_type
        
        # Available surrogate gradient types:
        # 'sigmoid', 'superspike', 'multigauss', 'triangle'
        self.snn_surrogate_type = snn_surrogate_type
        
        # Available temporal integration types:
        # 'weighted', 'attention', 'mean', 'sum'
        self.snn_temporal_integration = snn_temporal_integration
        
        # Noise scale for spike generation - reduced for stability
        self.snn_noise_scale = min(snn_noise_scale, 0.01)  # Cap noise at 0.01
        
        # Calculate feature dimensions
        base_features = self.time_interval_num
        enhanced_features = 12  # From build_cascade_graphs
        self.node_feature_dim = base_features
        self.graph_feature_dim = enhanced_features
        self.temporal_feature_dim = 6  # enhanced temporal features
        self.dynamics_feature_dim = 3  # cascade dynamics
        self.influence_feature_dim = 3 # influence features

        # Total node feature dimension - use actual calculation
        self.total_node_features = (self.Maxdepth - 2) + 17  # depth features + enhanced features
        
    def toString(self):
        s = ("pt:{} ot:{} tn:{} gnn_hs:{} snn_hs:{} gnn_l:{} snn_l:{} heads:{} "
             "gnn1:{} gnn:{} pool:{} neuron:{} surr:{} temp:{} b:{} lr:{}").format(
            self.pred_time, self.observation_time, self.time_interval_num,
            self.gnn_hidden_size, self.snn_hidden_size, 
            self.num_gnn_layers, self.num_snn_layers,
            self.num_attention_heads, self.gnn_first_layer_type, 
            self.gnn_layer_type, self.graph_pooling_type,
            self.snn_neuron_type, self.snn_surrogate_type,
            self.snn_temporal_integration, self.batch_size, self.lr)
        return s

# ============ EASY CONFIGURATION PRESETS ============

def get_gnn_config_presets():
    """Get predefined GNN configuration presets"""
    return {
        'gat_gcn': {
            'gnn_first_layer_type': 'gat',
            'gnn_layer_type': 'gcn',
            'graph_pooling_type': 'mean_max'
        },
        'gcn_only': {
            'gnn_first_layer_type': 'gcn',
            'gnn_layer_type': 'gcn', 
            'graph_pooling_type': 'mean_max'
        },
        'gat_only': {
            'gnn_first_layer_type': 'gat',
            'gnn_layer_type': 'gat',
            'graph_pooling_type': 'mean_max'
        },
        'sage_gin': {
            'gnn_first_layer_type': 'sage',
            'gnn_layer_type': 'gin',
            'graph_pooling_type': 'mean_max_add'
        },
        'transformer': {
            'gnn_first_layer_type': 'transformer',
            'gnn_layer_type': 'transformer',
            'graph_pooling_type': 'mean_max'
        },
        'residual_gat': {
            'gnn_first_layer_type': 'residual_gat',
            'gnn_layer_type': 'residual_gat',
            'graph_pooling_type': 'mean_max'
        }
    }

def get_snn_config_presets():
    """Get predefined SNN configuration presets"""
    return {
        'lif_sigmoid': {
            'snn_neuron_type': 'lif',
            'snn_surrogate_type': 'sigmoid',
            'snn_temporal_integration': 'weighted'
        },
        'lif_superspike': {
            'snn_neuron_type': 'lif', 
            'snn_surrogate_type': 'superspike',
            'snn_temporal_integration': 'weighted'
        },
        'adaptive_multigauss': {
            'snn_neuron_type': 'adaptive',
            'snn_surrogate_type': 'multigauss',
            'snn_temporal_integration': 'attention'
        },
        'if_triangle': {
            'snn_neuron_type': 'if',
            'snn_surrogate_type': 'triangle', 
            'snn_temporal_integration': 'mean'
        }
    }

def create_config_from_presets(dataset, pred_time, observation_time, time_interval,
                              gnn_preset='gat_gcn', snn_preset='lif_sigmoid', **kwargs):
    """Create config using presets for easy comparison"""
    gnn_presets = get_gnn_config_presets()
    snn_presets = get_snn_config_presets()
    
    # Merge preset configurations
    config_kwargs = {}
    config_kwargs.update(gnn_presets.get(gnn_preset, {}))
    config_kwargs.update(snn_presets.get(snn_preset, {}))
    config_kwargs.update(kwargs)  # Override with custom kwargs
    
    return Config(dataset, pred_time, observation_time, time_interval, **config_kwargs)
