# MyCas: GNN + SNN 级联预测模型

一个结合图神经网络(GNN)和脉冲神经网络(SNN)的先进级联预测模型，用于替代传统的Bi-LSTM方法进行信息级联预测。

## 📋 目录
- [背景与理论](#背景与理论)
- [模型架构](#模型架构)
- [安装与配置](#安装与配置)
- [快速开始](#快速开始)
- [训练指南](#训练指南)
- [评估与测试](#评估与测试)
- [模型配置](#模型配置)
- [高级用法](#高级用法)
- [结果与分析](#结果与分析)
- [故障排除](#故障排除)

## 🎯 背景与理论

### 信息级联预测
信息级联预测旨在预测信息在社交网络中传播的最终规模或增长模式。给定一条信息的早期传播模式（如转发、分享），目标是预测最终会有多少用户受到影响。

### 为什么选择 GNN + SNN？
传统方法使用RNN/LSTM进行时序建模，但存在局限性：
- **RNN/LSTM问题**：梯度消失、顺序处理瓶颈
- **GNN优势**：捕获级联图中的空间关系
- **SNN优势**：生物启发的时序处理、能效高、自然的脉冲编码

### 关键创新
1. **多尺度时序处理**：捕获不同时间范围的模式
2. **增强图构建**：5种节点特征，4种边类型
3. **交叉注意力融合**：结合GNN空间和SNN时序表示
4. **高级训练**：混合精度、梯度裁剪、余弦退火

## 🏗️ 模型架构

### 1. 数据处理流水线
```
原始级联数据 → 增强特征工程 → 多尺度图构建 → 
GNN编码 → SNN时序处理 → 交叉注意力融合 → MLP预测
```

### 2. 核心组件

#### 增强图构建
- **节点特征（5种类型）**：
  - 深度分布特征（原始级联结构）
  - 增强时序特征（速度、加速度、循环编码）
  - 结构图特征（熵、基尼系数、平衡性）
  - 级联动力学（增长率、病毒性、累积增长）
  - 影响传播（潜力、浅层vs深层影响）

- **边类型（4种类型）**：
  - 时序边（顺序连接）
  - 相似性边（余弦+结构相似性）
  - 影响边（基于影响强度的跳跃连接）
  - 自环（自适应权重）

#### 图神经网络(GNN)模块
- **多尺度处理**：4种不同时序尺度（1x, 2x, 4x, 8x）
- **层类型**：GAT、GCN、GraphSAGE、GIN、基于Transformer
- **注意力机制**：针对不同级联方面的多头注意力
- **特征聚合**：多尺度表示的拼接

#### 脉冲神经网络(SNN)模块
- **神经元类型**：LIF（漏积分发放）、IF、自适应
- **代理函数**：Sigmoid、SuperSpike、Triangle、MultiGauss
- **时序积分**：加权、指数、自适应
- **脉冲编码**：基于速率和时序的编码方案

#### 融合与预测
- **交叉注意力**：GNN和SNN特征之间
- **门控融合**：可学习的特征组合门
- **残差MLP**：用于鲁棒预测的跳跃连接
- **多损失训练**：结合MSE + MAE提高鲁棒性

## 🛠️ 安装与配置

### 依赖要求
```bash
# 核心依赖
torch>=1.10.1
torch-geometric>=2.0.0
numpy>=1.21.0
scikit-learn>=1.0.0
pandas>=1.3.0
matplotlib>=3.5.0
seaborn>=0.11.0

# 高级功能可选
tensorboard>=2.8.0
wandb>=0.12.0  # 实验跟踪
```

### 安装步骤
```bash
# 克隆仓库
git clone <repository-url>
cd mycas

# 创建conda环境
conda create -n mycas python=3.8
conda activate mycas

# 安装PyTorch（支持CUDA 12.0）
conda install pytorch torchvision torchaudio pytorch-cuda=12.0 -c pytorch -c nvidia

# 安装PyTorch Geometric
pip install torch-geometric

# 安装其他依赖
pip install -r requirements.txt

# 验证CUDA安装
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

### 数据集配置
下载CasFlow数据集（与Cas3D参考相同）：
- [Google Drive](https://drive.google.com/file/d/1o4KAZs19fl4Qa5LUtdnmNy57gHa15AF-/view?usp=sharing)
- [百度网盘（密码：`1msd`）](https://pan.baidu.com/s/1tWcEefxoRHj002F0s9BCTQ)

解压到`mycas/data/`目录（与ref/cas3d相同结构）：
```
mycas/data/
├── twitter/
│   └── dataset.txt
└── weibo/
    └── dataset.txt
```

**注意**：模型使用与Cas3D相同的数据集格式和分割策略进行公平比较。单个`dataset.txt`文件会自动分割为训练/验证/测试集（70%/15%/15%），使用相同的随机种子确保结果可重现。

## 🚀 快速开始

### 基础训练
```bash
# 使用默认设置在Twitter数据集上训练
python mycas/run_model.py twitter 0

# 在Weibo数据集上训练
python mycas/run_model.py weibo 0

# 使用特定GPU
python mycas/run_model.py twitter 1  # 使用GPU 1
```

### 快速测试
```bash
# 减少轮数的快速测试
python mycas/test_and_measure.py twitter --epochs 20 --runs 2
```

## 📚 训练指南

### 1. 基础训练

#### 单模型训练
```bash
# 使用默认配置的基础训练
python mycas/run_model.py twitter 0

# 使用自定义参数训练（编辑run_model.py）
# 修改GNN_PRESET和SNN_PRESET变量
GNN_PRESET = 'gat_gcn'      # 选项：'gat_gcn', 'gcn_only', 'gat_only'等
SNN_PRESET = 'lif_sigmoid'  # 选项：'lif_sigmoid', 'lif_superspike'等
```

#### 配置预设
可用的GNN预设：
- `'gat_gcn'`：GAT + GCN组合（推荐）
- `'gcn_only'`：纯GCN层
- `'gat_only'`：纯GAT层
- `'sage_gin'`：GraphSAGE + GIN
- `'transformer'`：图Transformer
- `'residual_gat'`：带残差连接的GAT

可用的SNN预设：
- `'lif_sigmoid'`：LIF神经元配sigmoid代理
- `'lif_superspike'`：LIF配SuperSpike代理
- `'if_triangle'`：IF神经元配triangle代理
- `'adaptive_multigauss'`：自适应神经元配MultiGauss

### 2. 数据集配置

#### Twitter数据集参数
```python
# 长期预测（32天）
config = Config(
    dataset='twitter',
    pred_time=3600*24*32,       # 32天预测
    observation_time=1*24*3600, # 1天观察
    time_interval=900,          # 15分钟间隔
    Maxdepth=5,                 # 最大级联深度
    gnn_hidden_size=128,
    snn_hidden_size=64,
    batch_size=32,
    lr=0.001
)
```

#### Weibo数据集参数
```python
# 短期预测（1天）
config = Config(
    dataset='weibo',
    pred_time=24*3600,          # 1天预测
    observation_time=1*3600,    # 1小时观察
    time_interval=36,           # 36秒间隔
    Maxdepth=30,                # 最大级联深度
    gnn_hidden_size=128,
    snn_hidden_size=64,
    batch_size=32,
    lr=0.001
)
```

### 3. 数据处理

#### 自动数据分割
模型使用与Cas3D相同的数据分割策略：
```python
# 自动分割（与ref/cas3d相同）
训练集：70%的有效级联
验证集：15%的有效级联
测试集：15%的有效级联

# 与Cas3D相同的过滤标准：
# - Weibo：按小时过滤（8-18或8-19，取决于观察时间）
# - Twitter：按日期过滤（4月10日之前）
# - 随机种子：0（确保结果可重现）
```

#### 特征工程
```python
# 深度分布特征（与Cas3D兼容）
time_interval_num = ceil(observation_time / time_interval)
depth_features = Maxdepth - 2  # 深度从2开始

# 增强特征（MyCas新增）
+ 增强时序特征
+ 结构图特征
+ 级联动力学
+ 影响传播特征
```

### 4. 训练监控

#### 进度跟踪
```bash
# 训练每10轮输出一次进度
Epoch 1/100: Train=0.2341, Val=0.2156, Test=0.2089/15.23%, LR=0.001000
Epoch 11/100: Train=0.1876, Val=0.1923, Test=0.1834/12.45%, LR=0.000950
...
Early stopping at epoch 67
```

#### 训练期间的关键指标
- **Train/Val/Test MSLE**：均方对数误差
- **MAPE**：平均绝对百分比误差
- **LR**：当前学习率（带调度）
- **Early Stopping**：防止过拟合（耐心值=10）

## 🧪 评估与测试

### 1. 综合测试套件

#### 完整模型比较
```bash
# 测试所有GNN和SNN组合
python mycas/test_and_measure.py twitter --epochs 50 --runs 3

# 测试特定配置
python mycas/test_and_measure.py weibo --gnn_types gat gcn --epochs 30 --runs 5

# 快速评估
python mycas/test_and_measure.py twitter --epochs 20 --runs 2
```

#### 自定义测试参数
```bash
# 使用特定数据集参数测试（匹配Cas3D格式）
python mycas/test_and_measure.py twitter \
    --pred_time 32 \           # 32天（为方便起见以天为单位）
    --obs_time 1 \             # 1天观察
    --time_interval 900 \      # 15分钟
    --epochs 50 \
    --runs 3
```

### 2. 评估指标

#### 主要指标（与Cas3D相同）
- **MSLE**（均方对数误差）：级联预测的主要指标
- **MAPE**（平均绝对百分比误差）：基于百分比的误差

#### 附加指标（MyCas扩展）
- **MAE**（平均绝对误差）：绝对差异
- **SMAPE**（对称MAPE）：对称百分比误差
- **R²**（决定系数）：决定系数
- **Pearson/Spearman相关性**：线性和秩相关

#### 高级分析
- **命中率**：误差范围内预测的百分比（10%、20%、50%）
- **基于规模的性能**：小/中/大级联的单独指标
- **趋势准确性**：预测增长/下降趋势的能力

### 3. 模型验证

#### 数据分割（与Cas3D兼容）
```python
# 与ref/cas3d相同的分割策略，确保公平比较
训练集：70%的有效级联
验证集：15%的有效级联（用于早停）
测试集：15%的有效级联（用于最终评估）

# 相同的随机种子（0）确保结果可重现
# 相同的时序约束过滤标准
```

#### 统计分析
```python
# 多次运行的结果聚合
均值 ± 标准差
最小值和最大值
置信区间
统计显著性检验
```

## ⚙️ 模型配置

### 1. GNN层类型

#### 图注意力网络（GAT）
```python
# 适用于：基于注意力的节点关系
config.gnn_layer_type = 'gat'
config.num_attention_heads = 4  # 多头注意力
```

#### 图卷积网络（GCN）
```python
# 适用于：基础结构学习
config.gnn_layer_type = 'gcn'
config.gnn_hidden_size = 128
```

#### GraphSAGE
```python
# 适用于：大规模图、归纳学习
config.gnn_layer_type = 'sage'
config.sage_aggregator = 'mean'  # 或'max', 'lstm'
```

#### 图同构网络（GIN）
```python
# 适用于：区分图结构
config.gnn_layer_type = 'gin'
config.gin_epsilon = 0.1
```

### 2. SNN神经元类型

#### 漏积分发放（LIF）
```python
# 最常见，良好的时序动力学
config.snn_neuron_type = 'lif'
config.snn_tau_mem = 10.0      # 膜时间常数
config.snn_tau_syn = 5.0       # 突触时间常数
config.snn_threshold = 1.0     # 发放阈值
```

#### 积分发放（IF）
```python
# 更简单，计算更快
config.snn_neuron_type = 'if'
config.snn_threshold = 1.0
```

#### 自适应神经元
```python
# 高级，自适应阈值
config.snn_neuron_type = 'adaptive'
config.snn_adaptation_rate = 0.1
```

### 3. 代理函数

#### Sigmoid
```python
# 平滑梯度，稳定训练
config.snn_surrogate_type = 'sigmoid'
config.surrogate_beta = 5.0    # 陡峭度参数
```

#### SuperSpike
```python
# 生物学合理，性能良好
config.snn_surrogate_type = 'superspike'
config.surrogate_beta = 100.0
```

#### Triangle
```python
# 快速计算，良好近似
config.snn_surrogate_type = 'triangle'
```

## 🔬 高级用法

### 1. 模型管理

#### 保存和恢复训练
```bash
# 列出可用模型
python mycas/model_resume.py list --dataset twitter

# 从检查点恢复训练
python mycas/model_resume.py resume --model_id twitter_gat_lif_20241201_143022 --epochs 30

# 比较不同检查点
python mycas/model_resume.py compare --model_id twitter_gat_lif_20241201_143022
```

#### 模型版本控制
```python
# 自动模型版本控制
模型ID格式：{dataset}_{gnn_type}_{snn_type}_{timestamp}
示例：twitter_gat_lif_20241201_143022

# 每个模型保存的文件：
mycas/experiments/twitter_gat_lif_20241201_143022/
├── best_model.pth          # 最佳性能检查点
├── model_epoch_10.pth      # 定期检查点
├── model_epoch_20.pth
├── metadata.json           # 模型配置和指标
└── training_log.json       # 详细训练历史
```

### 2. 实验跟踪

#### 综合日志记录
```python
# 自动实验跟踪
- 每轮训练指标
- 每轮验证指标
- 每轮测试指标
- 学习率调度
- 模型检查点
- 配置参数
- 训练时间跟踪
```

#### 结果分析
```bash
# 生成的分析文件
mycas/experiments/results_twitter_20241201_143022/
├── detailed_results.json      # 完整结果数据
├── summary_results.csv        # 汇总表
├── performance_comparison.png # 性能图表
├── metrics_distribution.png   # 指标可视化
└── analysis_report.txt        # 综合文本报告
```

### 3. 与Cas3D比较

#### 公平比较设置
```bash
# 使用与Cas3D相同的数据集和参数
# Twitter比较
python mycas/run_model.py twitter 0  # MyCas
python ref/cas3d/run_model.py twitter 0  # Cas3D参考

# Weibo比较
python mycas/run_model.py weibo 0  # MyCas
python ref/cas3d/run_model.py weibo 0  # Cas3D参考
```

#### 结果比较
```python
# 两个模型都使用：
- 相同的数据集文件（data/{dataset}/dataset.txt）
- 相同的训练/验证/测试分割（70%/15%/15%）
- 相同的随机种子（0）
- 相同的评估指标（MSLE、MAPE）
- 相同的目标变换（log2(y+1)）
```

## 📊 结果与分析

### 1. 性能基准

#### 预期性能范围
```
Twitter数据集（32天预测）：
- Cas3D基线：MSLE ~0.20-0.25，MAPE ~25-30%
- MyCas目标：MSLE <0.15，MAPE <20%

Weibo数据集（1天预测）：
- Cas3D基线：MSLE ~0.15-0.20，MAPE ~20-25%
- MyCas目标：MSLE <0.12，MAPE <18%
```

#### 典型训练时间
```
配置复杂度 vs 训练时间：
- 简单（GCN + IF）：~10-15分钟/轮
- 中等（GAT + LIF）：~15-25分钟/轮
- 复杂（多尺度 + 自适应）：~25-40分钟/轮

数据集大小影响：
- Twitter：比Weibo长约2-3倍
- 批大小32：基线
- 批大小16：约1.5倍长，通常收敛更好
```

### 2. 模型比较

#### 架构性能排名
```
典型性能排名（依赖于数据集）：
1. GAT + LIF（SuperSpike）：整体最佳
2. GAT + GCN + LIF（Sigmoid）：良好平衡
3. GraphSAGE + 自适应：适合大级联
4. GCN + IF：快速，性能不错
5. Transformer + LIF：高方差，潜在最佳
```

#### 配置推荐
```
对于Twitter（长期预测）：
- GNN：GAT或GAT+GCN组合
- SNN：LIF配SuperSpike代理
- 隐藏大小：128-256
- 注意力头：4-8

对于Weibo（短期预测）：
- GNN：GCN或GraphSAGE
- SNN：LIF配Sigmoid代理
- 隐藏大小：64-128
- 注意力头：2-4
```

### 3. 分析工具

#### 自动报告生成
```bash
# 生成综合分析
python mycas/test_and_measure.py twitter --epochs 50 --runs 3

# 输出包括：
- 性能比较图表
- 统计显著性检验
- 最佳配置识别
- 详细指标分解
- 训练时间分析
- 与Cas3D基线比较
```

#### 自定义分析
```python
# 加载结果进行自定义分析
import json
with open('mycas/experiments/results_twitter_20241201/detailed_results.json', 'r') as f:
    results = json.load(f)

# 提取特定指标
for config, metrics in results.items():
    print(f"{config}: MSLE={metrics['avg_test_msle']:.4f}")
```

## 🔧 故障排除

### 常见问题

#### 1. 数据集加载问题
```bash
# 确保正确的数据集结构
mycas/data/twitter/dataset.txt  # 单文件，与Cas3D相同
mycas/data/weibo/dataset.txt    # 单文件，与Cas3D相同

# 检查文件格式（制表符分隔）
cascade_id \t user_id \t timestamp \t num_adoptions \t adoption_paths
```

#### 2. CUDA内存不足
```bash
# 解决方案：
- 减少batch_size：32 → 16 → 8
- 减少hidden_size：256 → 128 → 64
- 使用梯度检查点（模型中自动）
- 使用更小的图（减少time_interval_num）
```

#### 3. CUDA初始化错误
```bash
# 如果遇到"CUDA error: initialization error"：
# 解决方案：
- 设置num_workers=0（已在代码中修复）
- 确保CUDA驱动正确安装
- 重启Python进程
```

#### 4. 训练不稳定
```bash
# 解决方案：
- 降低学习率：0.001 → 0.0005
- 增加梯度裁剪：max_norm=1.0 → 0.5
- 添加更多正则化：dropout=0.1 → 0.3
- 使用混合精度训练（自动）
```

#### 5. 收敛性差
```bash
# 解决方案：
- 增加模型容量：hidden_size、num_layers
- 尝试不同的GNN/SNN组合
- 调整学习率调度
- 增加训练轮数
- 检查数据预处理
```

### 性能优化

#### 1. 速度优化
```python
# 快速训练设置
config.batch_size = 64        # 更大批次
config.num_workers = 4        # 更多数据加载工作进程
config.pin_memory = True      # 更快GPU传输
config.epochs = 50            # 更少轮数快速结果
```

#### 2. 内存优化
```python
# 内存高效设置
config.batch_size = 8         # 更小批次
config.gnn_hidden_size = 64   # 更小隐藏维度
config.gradient_checkpointing = True  # 用计算换内存
```

#### 3. 精度优化
```python
# 高精度设置
config.epochs = 200           # 更多训练
config.lr = 0.0001           # 更低学习率
config.batch_size = 16        # 更小批次提高稳定性
config.num_attention_heads = 8 # 更多注意力
```

## 📈 使用示例

### 示例1：快速评估
```bash
# 快速测试最佳配置
python mycas/test_and_measure.py twitter --epochs 20 --runs 2 --gnn_types gat gcn
```

### 示例2：综合基准测试
```bash
# 具有统计显著性的完整基准测试
python mycas/test_and_measure.py twitter --epochs 100 --runs 5
```

### 示例3：与Cas3D比较
```bash
# 训练MyCas
python mycas/run_model.py twitter 0

# 训练Cas3D参考（用于比较）
python ref/cas3d/run_model.py twitter 0

# 由于相同的数据集和评估，结果将具有可比性
```

### 示例4：恢复中断的训练
```bash
# 列出可用模型
python mycas/model_resume.py list

# 恢复特定模型
python mycas/model_resume.py resume --model_id twitter_gat_lif_20241201_143022 --epochs 50
```

## 🔄 训练管理与恢复

### 早停机制
模型自动实现早停以防止过拟合：
- **耐心值**：10轮（验证损失不改善时停止）
- **最佳模型保存**：自动保存性能最佳的模型
- **检查点系统**：每轮保存模型状态用于恢复

### 恢复中断的训练
如果训练被中断（Ctrl+C、系统崩溃等），可以轻松恢复：

#### 快速恢复
```bash
# 自动恢复数据集的最新训练
python mycas/resume_training.py auto --dataset twitter --epochs 50

# 列出所有可用检查点
python mycas/resume_training.py list

# 恢复特定模型
python mycas/resume_training.py resume --model_id twitter_gat_lif_20241201_143022 --epochs 30
```

#### 代码中手动恢复
```python
# 从特定检查点恢复
config = Config(dataset='twitter', ...)
model = MyCas(config)

# 带恢复的训练
checkpoint_path = "mycas/checkpoints/twitter/twitter_gat_lif_20241201_143022_latest.pth"
train_msle, val_msle, test_msle, test_mape = model.train_model(X, Y, resume_from=checkpoint_path)
```

### 检查点管理
```bash
# 检查点结构
mycas/checkpoints/twitter/
├── twitter_gat_lif_20241201_143022_latest.pth    # 最新检查点
├── twitter_gat_lif_20241201_143022_best.pth      # 最佳模型
└── twitter_gcn_if_20241201_150000_latest.pth     # 其他模型

# 每个检查点包含：
- 模型权重和优化器状态
- 训练轮数和指标
- 完整配置用于精确恢复
- 调度器状态用于学习率
```

### 训练中断处理
```bash
# 如果训练被中断：
⚠️ Training interrupted at epoch 45
💾 Checkpoint saved. Resume with:
   python mycas/resume_training.py auto --dataset twitter

# 系统自动：
1. 保存当前模型状态
2. 保留优化器和调度器状态
3. 记录确切的轮数和指标
4. 提供恢复命令
```

### 高级恢复选项
```bash
# 使用不同参数恢复
python mycas/resume_training.py resume \
    --model_id twitter_gat_lif_20241201_143022 \
    --epochs 100  # 再训练100轮

# 列出特定数据集的检查点
python mycas/resume_training.py list --dataset weibo

# 自动查找并恢复最新模型
python mycas/resume_training.py auto --dataset twitter
```

## 📚 参考文献与引用

```bibtex
@article{mycas2024,
  title={MyCas: A Hybrid GNN-SNN Architecture for Information Cascade Prediction},
  author={Your Name},
  journal={Conference/Journal Name},
  year={2024}
}
```

## 📞 支持

如有问题和疑问：
1. 查看此README寻找常见解决方案
2. 查看故障排除部分
3. 检查仓库中的现有问题
4. 创建包含详细错误信息的新问题

---

**祝级联预测愉快！ 🚀**
