import torch
import json
import os
from datetime import datetime
from test_and_measure import ModelManager, CascadeTestSuite
from model import MyCas
from config import Config

class ModelResumer:
    """Resume training from checkpoints"""
    
    def __init__(self, base_dir="mycas/experiments"):
        self.model_manager = ModelManager(base_dir)
        self.test_suite = CascadeTestSuite(base_dir)
    
    def resume_training(self, model_id, additional_epochs=50):
        """Resume training from a specific model checkpoint"""
        
        # Find model directory
        model_dir = os.path.join(self.model_manager.base_dir, model_id)
        if not os.path.exists(model_dir):
            raise ValueError(f"Model directory not found: {model_dir}")
        
        # Load metadata
        metadata_path = os.path.join(model_dir, "metadata.json")
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        
        # Recreate config
        config_dict = metadata['config']
        config = Config(**{k: v for k, v in config_dict.items() 
                          if k in Config.__init__.__code__.co_varnames})
        
        # Load model
        model = MyCas(config)
        
        # Find latest checkpoint
        checkpoints = [f for f in os.listdir(model_dir) if f.startswith('model_epoch_')]
        if not checkpoints:
            raise ValueError("No checkpoints found")
        
        latest_checkpoint = max(checkpoints, key=lambda x: int(x.split('_')[-1].split('.')[0]))
        checkpoint_path = os.path.join(model_dir, latest_checkpoint)
        
        # Load checkpoint
        start_epoch, last_metrics = self.model_manager.load_model_state(
            checkpoint_path, model
        )
        
        print(f"Resuming training from epoch {start_epoch}")
        print(f"Last metrics: {last_metrics}")
        
        # Continue training
        config.epochs = start_epoch + additional_epochs
        
        # Load data
        from data_processor import load_cascade_data, preprocess_data
        X, Y = load_cascade_data(config)
        X, Y = preprocess_data(X, Y, config)
        
        # Resume training
        results = self.test_suite._train_and_evaluate_model(config, X, Y, f"resume_{start_epoch}")
        
        print(f"Training resumed and completed!")
        print(f"Final test MSLE: {results['test_metrics']['msle']:.4f}")
        
        return results
    
    def compare_checkpoints(self, model_id):
        """Compare different checkpoints of the same model"""
        model_dir = os.path.join(self.model_manager.base_dir, model_id)
        
        checkpoints = [f for f in os.listdir(model_dir) if f.startswith('model_epoch_')]
        checkpoints.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
        
        comparison_results = []
        
        for checkpoint in checkpoints:
            checkpoint_path = os.path.join(model_dir, checkpoint)
            checkpoint_data = torch.load(checkpoint_path, map_location='cpu')
            
            comparison_results.append({
                'checkpoint': checkpoint,
                'epoch': checkpoint_data['epoch'],
                'metrics': checkpoint_data['metrics']
            })
        
        return comparison_results
    
    def list_resumable_models(self, dataset=None):
        """List models that can be resumed"""
        models = self.model_manager.list_models(dataset=dataset)
        
        resumable = []
        for model in models:
            model_dir = os.path.join(self.model_manager.base_dir, model['model_id'])
            checkpoints = [f for f in os.listdir(model_dir) if f.startswith('model_epoch_')]
            
            if checkpoints:
                latest_epoch = max([int(f.split('_')[-1].split('.')[0]) for f in checkpoints])
                model['latest_epoch'] = latest_epoch
                model['num_checkpoints'] = len(checkpoints)
                resumable.append(model)
        
        return resumable

def main():
    """Resume training script"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Resume Model Training')
    parser.add_argument('action', choices=['resume', 'list', 'compare'], help='Action to perform')
    parser.add_argument('--model_id', type=str, help='Model ID to resume/compare')
    parser.add_argument('--dataset', type=str, help='Filter by dataset')
    parser.add_argument('--epochs', type=int, default=50, help='Additional epochs to train')
    
    args = parser.parse_args()
    
    resumer = ModelResumer()
    
    if args.action == 'list':
        models = resumer.list_resumable_models(args.dataset)
        print(f"Found {len(models)} resumable models:")
        print("-" * 80)
        for model in models:
            print(f"ID: {model['model_id']}")
            print(f"Dataset: {model['config']['dataset']}")
            print(f"GNN: {model['config']['gnn_layer_type']}")
            print(f"SNN: {model['config']['snn_neuron_type']}")
            print(f"Latest Epoch: {model['latest_epoch']}")
            print(f"Checkpoints: {model['num_checkpoints']}")
            print(f"Test MSLE: {model['metrics'].get('msle', 'N/A')}")
            print("-" * 40)
    
    elif args.action == 'resume':
        if not args.model_id:
            print("Error: --model_id required for resume action")
            return
        
        results = resumer.resume_training(args.model_id, args.epochs)
        print(f"Training completed! Final results: {results['test_metrics']}")
    
    elif args.action == 'compare':
        if not args.model_id:
            print("Error: --model_id required for compare action")
            return
        
        comparisons = resumer.compare_checkpoints(args.model_id)
        print(f"Checkpoint comparison for {args.model_id}:")
        print("-" * 60)
        for comp in comparisons:
            print(f"Epoch {comp['epoch']}: MSLE={comp['metrics'].get('msle', 'N/A'):.4f}")

if __name__ == "__main__":
    main()