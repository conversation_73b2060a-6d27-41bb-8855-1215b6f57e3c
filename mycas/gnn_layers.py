import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv, GINConv, TransformerConv
from torch_geometric.nn import global_mean_pool, global_max_pool, global_add_pool

class MultiHeadGATLayer(nn.Module):
    def __init__(self, in_features, out_features, num_heads=4, dropout=0.1):
        super(MultiHeadGATLayer, self).__init__()
        self.gat = GATConv(in_features, out_features, heads=num_heads, 
                          dropout=dropout, concat=False)
        self.norm = nn.LayerNorm(out_features)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, edge_index):
        x = self.gat(x, edge_index)
        x = self.norm(x)
        x = F.relu(x)
        x = self.dropout(x)
        return x

class GCNLayer(nn.Module):
    def __init__(self, in_features, out_features, dropout=0.1):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()
        self.gcn = GCNConv(in_features, out_features)
        self.norm = nn.LayerNorm(out_features)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, edge_index):
        x = self.gcn(x, edge_index)
        x = self.norm(x)
        x = F.relu(x)
        x = self.dropout(x)
        return x

class GraphSAGELayer(nn.Module):
    def __init__(self, in_features, out_features, dropout=0.1, aggr='mean'):
        super(GraphSAGELayer, self).__init__()
        self.sage = SAGEConv(in_features, out_features, aggr=aggr)
        self.norm = nn.LayerNorm(out_features)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, edge_index):
        x = self.sage(x, edge_index)
        x = self.norm(x)
        x = F.relu(x)
        x = self.dropout(x)
        return x

class GINLayer(nn.Module):
    def __init__(self, in_features, out_features, dropout=0.1):
        super(GINLayer, self).__init__()
        mlp = nn.Sequential(
            nn.Linear(in_features, out_features),
            nn.ReLU(),
            nn.Linear(out_features, out_features)
        )
        self.gin = GINConv(mlp)
        self.norm = nn.LayerNorm(out_features)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, edge_index):
        x = self.gin(x, edge_index)
        x = self.norm(x)
        x = F.relu(x)
        x = self.dropout(x)
        return x

class GraphTransformerLayer(nn.Module):
    def __init__(self, in_features, out_features, num_heads=4, dropout=0.1):
        super(GraphTransformerLayer, self).__init__()
        self.transformer = TransformerConv(in_features, out_features, 
                                         heads=num_heads, dropout=dropout, concat=False)
        self.norm = nn.LayerNorm(out_features)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, edge_index):
        x = self.transformer(x, edge_index)
        x = self.norm(x)
        x = F.relu(x)
        x = self.dropout(x)
        return x

class ResidualGNNLayer(nn.Module):
    def __init__(self, gnn_type, in_features, out_features, **kwargs):
        super(ResidualGNNLayer, self).__init__()
        self.gnn_layer = self._get_gnn_layer(gnn_type, in_features, out_features, **kwargs)
        self.residual_proj = nn.Linear(in_features, out_features) if in_features != out_features else nn.Identity()
        
    def _get_gnn_layer(self, gnn_type, in_features, out_features, **kwargs):
        if gnn_type == 'gcn':
            return GCNLayer(in_features, out_features, **kwargs)
        elif gnn_type == 'gat':
            return MultiHeadGATLayer(in_features, out_features, **kwargs)
        elif gnn_type == 'sage':
            return GraphSAGELayer(in_features, out_features, **kwargs)
        elif gnn_type == 'gin':
            return GINLayer(in_features, out_features, **kwargs)
        elif gnn_type == 'transformer':
            return GraphTransformerLayer(in_features, out_features, **kwargs)
        else:
            raise ValueError(f"Unknown GNN type: {gnn_type}")
    
    def forward(self, x, edge_index):
        residual = self.residual_proj(x)
        x = self.gnn_layer(x, edge_index)
        return x + residual

class TemporalGNNEncoder(nn.Module):
    def __init__(self, config):
        super(TemporalGNNEncoder, self).__init__()
        self.config = config
        
        # Use a flexible input dimension - will be set dynamically
        self.input_dim = None
        self.input_proj = None
        
        # Initialize with default dimension, will be updated if needed
        default_input_dim = getattr(config, 'total_node_features', config.time_interval_num + 12)
        self._init_layers(default_input_dim)

    def _init_layers(self, input_dim):
        """Initialize layers with given input dimension"""
        self.input_dim = input_dim
        self.input_proj = nn.Linear(input_dim, self.config.gnn_hidden_size)
        
        # GNN layers - CONFIGURABLE HERE
        self.gnn_layers = nn.ModuleList()
        for i in range(self.config.num_gnn_layers):
            if i == 0:
                # First layer type - CHANGE config.gnn_first_layer_type
                layer = self._create_gnn_layer(
                    self.config.gnn_first_layer_type, 
                    self.config.gnn_hidden_size, 
                    self.config.gnn_hidden_size,
                    self.config
                )
            else:
                # Subsequent layer type - CHANGE config.gnn_layer_type
                layer = self._create_gnn_layer(
                    self.config.gnn_layer_type,
                    self.config.gnn_hidden_size,
                    self.config.gnn_hidden_size,
                    self.config
                )
            self.gnn_layers.append(layer)
        
        # Temporal attention
        self.temporal_attention = nn.MultiheadAttention(
            self.config.gnn_hidden_size, self.config.num_attention_heads, 
            dropout=self.config.dropout, batch_first=True
        )
        
        # Graph pooling - CONFIGURABLE HERE
        pooling_dim = self._get_pooling_dim(self.config.graph_pooling_type)
        self.pool_proj = nn.Linear(pooling_dim * self.config.gnn_hidden_size, self.config.gnn_hidden_size)
    
    def _create_gnn_layer(self, layer_type, in_features, out_features, config):
        """Create GNN layer based on type - MAIN CONFIGURATION POINT"""
        if layer_type == 'gcn':
            return GCNLayer(in_features, out_features, config.dropout)
        elif layer_type == 'gat':
            return MultiHeadGATLayer(in_features, out_features, 
                                   config.num_attention_heads, config.dropout)
        elif layer_type == 'sage':
            return GraphSAGELayer(in_features, out_features, config.dropout)
        elif layer_type == 'gin':
            return GINLayer(in_features, out_features, config.dropout)
        elif layer_type == 'transformer':
            return GraphTransformerLayer(in_features, out_features,
                                       config.num_attention_heads, config.dropout)
        elif layer_type == 'residual_gcn':
            return ResidualGNNLayer('gcn', in_features, out_features, dropout=config.dropout)
        elif layer_type == 'residual_gat':
            return ResidualGNNLayer('gat', in_features, out_features, 
                                  num_heads=config.num_attention_heads, dropout=config.dropout)
        else:
            raise ValueError(f"Unknown GNN layer type: {layer_type}")
    
    def _get_pooling_dim(self, pooling_type):
        """Get pooling dimension multiplier"""
        if pooling_type in ['mean', 'max', 'add']:
            return 1
        elif pooling_type == 'mean_max':
            return 2
        elif pooling_type == 'mean_max_add':
            return 3
        else:
            return 2  # default
    
    def _apply_pooling(self, x, batch, pooling_type):
        """Apply graph pooling - CONFIGURABLE HERE"""
        if pooling_type == 'mean':
            return global_mean_pool(x, batch)
        elif pooling_type == 'max':
            return global_max_pool(x, batch)
        elif pooling_type == 'add':
            return global_add_pool(x, batch)
        elif pooling_type == 'mean_max':
            x_mean = global_mean_pool(x, batch)
            x_max = global_max_pool(x, batch)
            return torch.cat([x_mean, x_max], dim=1)
        elif pooling_type == 'mean_max_add':
            x_mean = global_mean_pool(x, batch)
            x_max = global_max_pool(x, batch)
            x_add = global_add_pool(x, batch)
            return torch.cat([x_mean, x_max, x_add], dim=1)
        else:
            # Default: mean + max
            x_mean = global_mean_pool(x, batch)
            x_max = global_max_pool(x, batch)
            return torch.cat([x_mean, x_max], dim=1)
        
    def forward(self, node_features, edge_index, batch, temporal_mask=None):
        # Input projection
        x = self.input_proj(node_features)
        
        # Apply GNN layers with residual connections
        for layer in self.gnn_layers:
            if hasattr(layer, 'residual_proj'):  # ResidualGNNLayer
                x = layer(x, edge_index)
            else:
                x_new = layer(x, edge_index)
                x = x + x_new  # Manual residual connection
        
        # Global pooling
        x_pooled = self._apply_pooling(x, batch, self.config.graph_pooling_type)
        x_pooled = self.pool_proj(x_pooled)
        
        return x_pooled, x  # Return both pooled and node-level features
