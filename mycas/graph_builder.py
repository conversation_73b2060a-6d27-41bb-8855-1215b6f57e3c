import numpy as np
import torch
from torch_geometric.data import Data, Batch
from sklearn.metrics.pairwise import cosine_similarity
import networkx as nx

class EnhancedCascadeGraphBuilder:
    def __init__(self, config):
        self.config = config
        self.max_nodes = config.time_interval_num
        
    def build_cascade_graph(self, cascade_data, temporal_features=None):
        """Build enhanced cascade graph with multiple edge types"""
        time_steps = cascade_data.shape[0]
        
        # Enhanced node features
        node_features = []
        
        for t in range(time_steps):
            # 1. Depth distribution features (original)
            depth_feat = cascade_data[t]
            
            # 2. Enhanced temporal features
            temporal_feat = self._get_enhanced_temporal_features(t, time_steps, cascade_data)
            
            # 3. Structural graph features
            graph_feat = self._get_enhanced_graph_features(t, cascade_data)
            
            # 4. Cascade dynamics features
            dynamics_feat = self._get_cascade_dynamics(t, cascade_data)
            
            # 5. Influence propagation features
            influence_feat = self._get_influence_features(t, cascade_data)
            
            # Combine all features
            node_feat = np.concatenate([
                depth_feat, temporal_feat, graph_feat, 
                dynamics_feat, influence_feat
            ])
            node_features.append(node_feat)
        
        # Build multi-type edges
        edge_index, edge_attr = self._build_enhanced_edges(cascade_data, time_steps)
        
        # Convert to tensors
        node_features = torch.FloatTensor(np.array(node_features))
        edge_index = torch.LongTensor(edge_index)
        edge_attr = torch.FloatTensor(edge_attr) if len(edge_attr) > 0 else None
        
        return Data(x=node_features, edge_index=edge_index, edge_attr=edge_attr)
    
    def _get_enhanced_temporal_features(self, t, time_steps, cascade_data):
        """Enhanced temporal features"""
        features = []
        
        # Basic temporal position
        features.extend([
            t / max(time_steps - 1, 1),  # Normalized position
            np.sin(2 * np.pi * t / time_steps),  # Cyclic encoding
            np.cos(2 * np.pi * t / time_steps)
        ])
        
        # Temporal velocity (rate of change)
        if t > 0:
            velocity = np.sum(cascade_data[t] - cascade_data[t-1])
            features.append(velocity)
        else:
            features.append(0.0)
        
        # Temporal acceleration
        if t > 1:
            accel = np.sum(cascade_data[t] - 2*cascade_data[t-1] + cascade_data[t-2])
            features.append(accel)
        else:
            features.append(0.0)
        
        # Time since peak activity
        peak_time = np.argmax([np.sum(cascade_data[i]) for i in range(min(t+1, time_steps))])
        features.append((t - peak_time) / max(time_steps - 1, 1))
        
        return np.array(features)
    
    def _get_enhanced_graph_features(self, t, cascade_data):
        """Enhanced graph structural features"""
        current_dist = cascade_data[t]
        features = []
        
        # Distribution statistics
        total_nodes = np.sum(current_dist)
        if total_nodes > 0:
            # Depth diversity (entropy)
            probs = current_dist / total_nodes
            probs = probs[probs > 0]
            entropy = -np.sum(probs * np.log(probs + 1e-8))
            features.append(entropy)
            
            # Depth concentration (Gini coefficient)
            sorted_dist = np.sort(current_dist)
            n = len(sorted_dist)
            gini = (2 * np.sum((np.arange(n) + 1) * sorted_dist)) / (n * np.sum(sorted_dist)) - (n + 1) / n
            features.append(gini)
            
            # Structural balance
            max_depth = len(current_dist)
            weighted_depth = np.sum(np.arange(max_depth) * current_dist) / total_nodes
            features.append(weighted_depth / max(max_depth - 1, 1))
        else:
            features.extend([0.0, 0.0, 0.0])
        
        # Cascade width and depth
        active_depths = np.sum(current_dist > 0)
        max_width = np.max(current_dist) if len(current_dist) > 0 else 0
        features.extend([active_depths / len(current_dist), max_width])
        
        return np.array(features)
    
    def _get_cascade_dynamics(self, t, cascade_data):
        """Cascade dynamics features"""
        features = []
        time_steps = cascade_data.shape[0]
        
        # Growth rate
        if t > 0:
            prev_total = np.sum(cascade_data[t-1])
            curr_total = np.sum(cascade_data[t])
            growth_rate = (curr_total - prev_total) / max(prev_total, 1)
            features.append(growth_rate)
        else:
            features.append(0.0)
        
        # Cumulative growth
        cumulative = np.sum([np.sum(cascade_data[i]) for i in range(t+1)])
        features.append(cumulative)
        
        # Virality score (how fast it spreads)
        if t > 0:
            virality = np.sum(cascade_data[t]) / (t + 1)
            features.append(virality)
        else:
            features.append(np.sum(cascade_data[0]))
        
        return np.array(features)
    
    def _get_influence_features(self, t, cascade_data):
        """Influence propagation features"""
        features = []
        
        # Influence potential (remaining capacity)
        current_total = np.sum(cascade_data[t])
        max_observed = np.max([np.sum(cascade_data[i]) for i in range(t+1)])
        influence_potential = max_observed - current_total
        features.append(influence_potential)
        
        # Depth-wise influence
        if len(cascade_data[t]) > 1:
            # Shallow vs deep influence
            shallow_influence = np.sum(cascade_data[t][:len(cascade_data[t])//2])
            deep_influence = np.sum(cascade_data[t][len(cascade_data[t])//2:])
            features.extend([shallow_influence, deep_influence])
        else:
            features.extend([0.0, 0.0])
        
        return np.array(features)
    
    def _build_enhanced_edges(self, cascade_data, time_steps):
        """Build enhanced edges with multiple types"""
        edges = []
        edge_attributes = []
        
        # 1. Temporal edges (sequential)
        for t in range(time_steps - 1):
            # Forward temporal edge
            edges.append([t, t + 1])
            edge_attributes.append([1.0, 0.0, 0.0])  # [temporal, similarity, influence]
            
            # Backward temporal edge
            edges.append([t + 1, t])
            edge_attributes.append([1.0, 0.0, 0.0])
        
        # 2. Similarity-based edges
        for i in range(time_steps):
            for j in range(i + 1, time_steps):
                # Calculate multiple similarities
                cosine_sim = self._cosine_similarity(cascade_data[i], cascade_data[j])
                structural_sim = self._structural_similarity(cascade_data[i], cascade_data[j])
                
                combined_sim = 0.7 * cosine_sim + 0.3 * structural_sim
                
                if combined_sim > 0.3:  # Threshold for connection
                    edges.append([i, j])
                    edge_attributes.append([0.0, combined_sim, 0.0])
                    
                    edges.append([j, i])
                    edge_attributes.append([0.0, combined_sim, 0.0])
        
        # 3. Influence-based edges (skip connections)
        for i in range(time_steps):
            for j in range(i + 2, min(i + 6, time_steps)):  # Skip 1-4 steps
                influence_strength = self._calculate_influence(cascade_data[i], cascade_data[j], j - i)
                
                if influence_strength > 0.2:
                    edges.append([i, j])
                    edge_attributes.append([0.0, 0.0, influence_strength])
        
        # 4. Self-loops with adaptive weights
        for t in range(time_steps):
            self_weight = np.sum(cascade_data[t]) / (np.sum(cascade_data) + 1e-8)
            edges.append([t, t])
            edge_attributes.append([0.0, 0.0, self_weight])
        
        if not edges:
            return np.array([[], []]), []
        
        return np.array(edges).T, edge_attributes
    
    def _cosine_similarity(self, vec1, vec2):
        """Calculate cosine similarity"""
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def _structural_similarity(self, dist1, dist2):
        """Calculate structural similarity based on distribution shape"""
        if np.sum(dist1) == 0 or np.sum(dist2) == 0:
            return 0.0
        
        # Normalize distributions
        norm_dist1 = dist1 / np.sum(dist1)
        norm_dist2 = dist2 / np.sum(dist2)
        
        # Jensen-Shannon divergence
        m = 0.5 * (norm_dist1 + norm_dist2)
        js_div = 0.5 * self._kl_divergence(norm_dist1, m) + 0.5 * self._kl_divergence(norm_dist2, m)
        
        return 1.0 - js_div
    
    def _kl_divergence(self, p, q):
        """Calculate KL divergence"""
        epsilon = 1e-10
        p = np.clip(p, epsilon, 1.0)
        q = np.clip(q, epsilon, 1.0)
        return np.sum(p * np.log(p / q))
    
    def _calculate_influence(self, source_dist, target_dist, time_gap):
        """Calculate influence strength between time steps"""
        source_total = np.sum(source_dist)
        target_total = np.sum(target_dist)
        
        if source_total == 0:
            return 0.0
        
        # Influence decays with time
        time_decay = np.exp(-0.1 * time_gap)
        
        # Influence strength based on growth
        growth = (target_total - source_total) / source_total
        influence = max(0, growth) * time_decay
        
        return min(influence, 1.0)
    
    def batch_graphs(self, graph_list):
        """Batch multiple graphs"""
        return Batch.from_data_list(graph_list)

def build_cascade_graphs(cascades, config):
    """Build enhanced graphs for a batch of cascades"""
    builder = EnhancedCascadeGraphBuilder(config)
    graphs = []
    
    # First, build one graph to determine actual feature dimensions
    sample_cascade = cascades[0].cpu().numpy() if torch.is_tensor(cascades[0]) else cascades[0]
    sample_graph = builder.build_cascade_graph(sample_cascade)
    actual_features = sample_graph.x.shape[1]
    
    # Update config with actual feature dimension
    config.actual_node_features = actual_features
    
    graphs = [sample_graph]
    
    for cascade in cascades[1:]:
        cascade_data = cascade.cpu().numpy() if torch.is_tensor(cascade) else cascade
        graph = builder.build_cascade_graph(cascade_data)
        
        # Ensure consistent dimensions
        if graph.x.shape[1] != actual_features:
            if graph.x.shape[1] < actual_features:
                padding = torch.zeros((graph.x.shape[0], actual_features - graph.x.shape[1]))
                graph.x = torch.cat([graph.x, padding], dim=1)
            else:
                graph.x = graph.x[:, :actual_features]
        
        graphs.append(graph)
    
    return builder.batch_graphs(graphs)
